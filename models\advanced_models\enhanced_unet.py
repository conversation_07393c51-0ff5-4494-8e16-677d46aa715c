"""
Enhanced UNet Implementation using MONAI
True U-Net architecture with skip connections for well log imputation
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from monai.networks.nets import UNet
from monai.networks.layers import Norm
from .base_model import BaseAdvancedModel
from typing import Dict, Any, Optional, List

class EnhancedUNet(BaseAdvancedModel):
    """
    Enhanced U-Net model using MONAI framework.
    True U-Net architecture with skip connections for spatial pattern recognition.
    """

    def __init__(self, n_features=4, sequence_len=64,
                 channels=(32, 64, 128, 256), strides=(2, 2, 2),
                 epochs=50, batch_size=32, learning_rate=1e-4, **kwargs):
        """
        Initialize Enhanced UNet model.

        Args:
            n_features: Number of log features
            sequence_len: Length of input sequences
            channels: Channel sizes for each level of U-Net
            strides: Stride sizes for downsampling
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, **kwargs)

        self.channels = channels
        self.strides = strides
        self.learning_rate = learning_rate

        # Validate parameters
        self._validate_parameters()

        # Training components
        self.optimizer = None
        self.criterion = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if len(self.channels) != len(self.strides) + 1:
            raise ValueError(f"channels length ({len(self.channels)}) must be strides length + 1 ({len(self.strides) + 1})")

        if self.sequence_len < 32:
            print("⚠️ Warning: sequence_len < 32 may not be optimal for U-Net architecture")

        # Check if sequence length is compatible with strides
        min_size = self.sequence_len
        for stride in self.strides:
            min_size = min_size // stride
        if min_size < 4:
            print(f"⚠️ Warning: sequence_len {self.sequence_len} may be too small for given strides {self.strides}")

    def _initialize_model(self) -> None:
        """Initialize the MONAI UNet model."""
        try:
            # Create 1D U-Net for sequence data (treating features as channels)
            self.model = UNet(
                spatial_dims=1,  # 1D for sequence data
                in_channels=self.n_features,  # Number of features as input channels
                out_channels=self.n_features,  # Same number of output channels
                channels=self.channels,
                strides=self.strides,
                num_res_units=2,  # Residual units per level
                norm=Norm.BATCH,
                dropout=0.1,
                bias=True
            ).to(self.device)

            # Initialize optimizer and loss function
            self.optimizer = optim.Adam(self.model.parameters(), lr=self.learning_rate)
            self.criterion = nn.MSELoss()

            print(f"✅ Enhanced UNet initialized with channels {self.channels}")
            print(f"   Device: {self.device}, Spatial dims: 1D")
            print(f"   Input/Output channels: {self.n_features}")
            print(f"   Total parameters: {sum(p.numel() for p in self.model.parameters()):,}")

        except Exception as e:
            raise RuntimeError(f"Failed to initialize Enhanced UNet model: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Prepare data for U-Net training/prediction.

        Args:
            data: Input data tensor (batch, sequence, features)
            truth_data: Ground truth data (optional, for training)

        Returns:
            Dictionary with prepared tensors
        """
        # Reshape data for 1D U-Net: (batch, features, sequence)
        # Input format: (batch, sequence, features) -> (batch, features, sequence)
        if len(data.shape) == 3:
            data_reshaped = data.transpose(1, 2)  # Swap sequence and features dimensions
        else:
            raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {data.shape}")

        data_reshaped = data_reshaped.to(self.device)

        result = {'input': data_reshaped}

        if truth_data is not None:
            if len(truth_data.shape) == 3:
                truth_reshaped = truth_data.transpose(1, 2)  # Same transformation
            else:
                raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {truth_data.shape}")
            truth_reshaped = truth_reshaped.to(self.device)
            result['target'] = truth_reshaped

        return result

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None) -> None:
        """
        Train the Enhanced UNet model.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
        """
        if self.model is None:
            self._initialize_model()

        epochs = epochs or self.epochs
        batch_size = batch_size or self.batch_size
        
        print(f"Training Enhanced UNet for {epochs} epochs with batch size {batch_size}...")
        print(f"Input data shape: {train_data.shape}")

        # Convert to tensors if needed
        if not isinstance(train_data, torch.Tensor):
            train_data = torch.tensor(train_data, dtype=torch.float32)
        if not isinstance(truth_data, torch.Tensor):
            truth_data = torch.tensor(truth_data, dtype=torch.float32)

        self.model.train()
        training_losses = []
        
        # Process data in batches
        n_samples = train_data.shape[0]
        n_batches = (n_samples + batch_size - 1) // batch_size
        
        for epoch in range(epochs):
            epoch_losses = []
            
            for batch_idx in range(n_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, n_samples)
                
                # Get batch data
                batch_train = train_data[start_idx:end_idx]
                batch_truth = truth_data[start_idx:end_idx]
                
                # Prepare data
                data_dict = self._prepare_data(batch_train, batch_truth)
                input_data = data_dict['input']
                target_data = data_dict['target']

                # Create missing value mask
                missing_mask = torch.isnan(input_data)
                input_data_filled = input_data.clone()
                input_data_filled[missing_mask] = 0.0  # Fill NaN with zeros for U-Net

                self.optimizer.zero_grad()

                # Forward pass
                outputs = self.model(input_data_filled)

                # Ensure output and target have the same shape
                if outputs.shape != target_data.shape:
                    print(f"⚠️ Shape mismatch: outputs {outputs.shape} vs target {target_data.shape}")
                    # If shapes don't match, use full loss without masking
                    loss = self.criterion(outputs, target_data)
                else:
                    # Calculate loss only on non-missing values
                    valid_mask = ~missing_mask
                    if valid_mask.sum() > 0:
                        # Extract valid elements and ensure they have the same count
                        output_valid = outputs[valid_mask]
                        target_valid = target_data[valid_mask]
                        
                        # Double-check shapes before loss calculation
                        if output_valid.shape == target_valid.shape:
                            loss = self.criterion(output_valid, target_valid)
                        else:
                            print(f"⚠️ Valid mask shape mismatch: {output_valid.shape} vs {target_valid.shape}")
                            # Fallback to full loss
                            loss = self.criterion(outputs, target_data)
                    else:
                        loss = self.criterion(outputs, target_data)

                # Backward pass
                loss.backward()
                self.optimizer.step()

                epoch_losses.append(loss.item())
            
            # Average loss for the epoch
            avg_epoch_loss = np.mean(epoch_losses)
            training_losses.append(avg_epoch_loss)

            if (epoch + 1) % 10 == 0:
                print(f"   Epoch {epoch + 1}/{epochs}, Loss: {avg_epoch_loss:.6f}")

        self.is_fitted = True
        self.training_history = {'losses': training_losses}
        print("Enhanced UNet training completed!")

    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values using Enhanced UNet.

        Args:
            data: Input data with missing values (batch, sequence, features)

        Returns:
            Imputed data tensor (batch, sequence, features)
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")

        # Convert to tensor if needed
        if not isinstance(data, torch.Tensor):
            data = torch.tensor(data, dtype=torch.float32)

        self.model.eval()
        
        # Process in batches to avoid memory issues
        n_samples = data.shape[0]
        batch_size = min(self.batch_size, n_samples)
        n_batches = (n_samples + batch_size - 1) // batch_size
        
        results = []

        with torch.no_grad():
            for batch_idx in range(n_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, n_samples)
                
                # Get batch data
                batch_data = data[start_idx:end_idx]
                
                # Prepare data - converts to (batch, features, sequence)
                data_dict = self._prepare_data(batch_data)
                input_data = data_dict['input']

                # Handle missing values
                missing_mask = torch.isnan(input_data)
                input_data_filled = input_data.clone()
                input_data_filled[missing_mask] = 0.0

                # Predict - output is (batch, features, sequence)
                outputs = self.model(input_data_filled)

                # Combine original and predicted values
                result = input_data.clone()
                result[missing_mask] = outputs[missing_mask]

                # Transpose back to original format: (batch, features, sequence) -> (batch, sequence, features)
                result = result.transpose(1, 2)
                
                results.append(result.cpu())
        
        # Concatenate all batch results
        final_result = torch.cat(results, dim=0)
        return final_result

    def get_model_complexity(self) -> Dict[str, int]:
        """Get model complexity metrics."""
        if self.model is None:
            self._initialize_model()

        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)

        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'channels': self.channels,
            'strides': self.strides,
            'complexity_score': 2  # Medium-high complexity
        }

    def get_feature_maps(self, data: torch.Tensor, layer_name: str = 'encoder') -> Optional[torch.Tensor]:
        """
        Extract feature maps from specified layer.

        Args:
            data: Input data tensor
            layer_name: Name of layer to extract features from

        Returns:
            Feature maps tensor or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting feature maps")
            return None

        # This would require hooks to extract intermediate features
        # For now, return None and implement in future versions
        print("ℹ️ Feature map extraction not yet implemented")
        return None
