#!/usr/bin/env python3
"""
Test script to verify TEFN visualization fixes
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add the example directory to the path
sys.path.append('example')

# Import the fixed functions
from Pypots_quick_start_tutor import (
    debug_array_shapes, 
    validate_and_reshape_forecasting_data,
    plot_forecasting_results,
    plot_enhanced_tefn_forecasting,
    setup_plotting
)

def create_test_data():
    """Create synthetic test data that mimics the TEFN output structure."""
    print("🔧 Creating synthetic test data...")
    
    # Simulate PhysioNet-like data structure
    n_samples = 10
    n_hist_steps = 42  # Historical steps (48 - 6)
    n_future_steps = 6  # Prediction steps
    n_features = 37    # Number of features
    
    # Create 3D historical data (samples, time_steps, features)
    historical_data = np.random.randn(n_samples, n_hist_steps, n_features) * 2 + 5
    
    # Create 3D true future data (samples, time_steps, features)
    true_future = np.random.randn(n_samples, n_future_steps, n_features) * 2 + 5
    
    # Create 3D predictions (samples, time_steps, features) - this is where the dimension issue occurs
    predictions = np.random.randn(n_samples, n_future_steps, n_features) * 2 + 5
    
    print(f"✅ Test data created:")
    debug_array_shapes("Test Historical Data", historical_data)
    debug_array_shapes("Test True Future", true_future)
    debug_array_shapes("Test Predictions", predictions)
    
    return historical_data, true_future, predictions

def test_dimension_validation():
    """Test the dimension validation and reshaping functions."""
    print("\n" + "="*60)
    print("🧪 Testing Dimension Validation Functions")
    print("="*60)
    
    historical_data, true_future, predictions = create_test_data()
    
    # Test the validation function
    try:
        hist_reshaped, true_reshaped, pred_reshaped = validate_and_reshape_forecasting_data(
            historical_data, true_future, predictions, "TEST"
        )
        
        print("\n✅ Dimension validation successful!")
        print("📊 Reshaped data:")
        debug_array_shapes("Reshaped Historical", hist_reshaped)
        debug_array_shapes("Reshaped True Future", true_reshaped)
        debug_array_shapes("Reshaped Predictions", pred_reshaped)
        
        return hist_reshaped, true_reshaped, pred_reshaped
        
    except Exception as e:
        print(f"❌ Dimension validation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_standard_visualization():
    """Test the standard forecasting visualization."""
    print("\n" + "="*60)
    print("🧪 Testing Standard Forecasting Visualization")
    print("="*60)
    
    historical_data, true_future, predictions = create_test_data()
    
    # Calculate a mock MAE
    mae_score = 0.4738
    
    try:
        plot_forecasting_results(
            historical_data=historical_data,
            true_future=true_future,
            predictions=predictions,
            model_name="TEST_TEFN",
            mae_score=mae_score
        )
        print("✅ Standard visualization test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Standard visualization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_visualization():
    """Test the enhanced TEFN forecasting visualization."""
    print("\n" + "="*60)
    print("🧪 Testing Enhanced TEFN Visualization")
    print("="*60)
    
    historical_data, true_future, predictions = create_test_data()
    
    # Calculate a mock MAE
    mae_score = 0.4738
    
    try:
        plot_enhanced_tefn_forecasting(
            historical_data=historical_data,
            true_future=true_future,
            predictions=predictions,
            model_name="TEST_TEFN_ENHANCED",
            mae_score=mae_score
        )
        print("✅ Enhanced visualization test successful!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced visualization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases that might cause dimension mismatches."""
    print("\n" + "="*60)
    print("🧪 Testing Edge Cases")
    print("="*60)
    
    # Test case 1: Mismatched prediction dimensions
    print("\n🔍 Test Case 1: Mismatched prediction dimensions")
    historical_data = np.random.randn(5, 42, 37)
    true_future = np.random.randn(5, 6, 37)
    predictions = np.random.randn(5, 8, 37)  # Wrong time dimension
    
    try:
        plot_forecasting_results(
            historical_data=historical_data,
            true_future=true_future,
            predictions=predictions,
            model_name="TEST_MISMATCH",
            mae_score=0.5
        )
        print("✅ Handled mismatched dimensions successfully!")
    except Exception as e:
        print(f"❌ Failed to handle mismatched dimensions: {str(e)}")
    
    # Test case 2: 2D data (should work without issues)
    print("\n🔍 Test Case 2: 2D data")
    historical_data_2d = np.random.randn(5, 42)
    true_future_2d = np.random.randn(5, 6)
    predictions_2d = np.random.randn(5, 6)
    
    try:
        plot_forecasting_results(
            historical_data=historical_data_2d,
            true_future=true_future_2d,
            predictions=predictions_2d,
            model_name="TEST_2D",
            mae_score=0.3
        )
        print("✅ Handled 2D data successfully!")
    except Exception as e:
        print(f"❌ Failed to handle 2D data: {str(e)}")

def main():
    """Run all tests."""
    print("🚀 Starting TEFN Visualization Fix Tests")
    print("="*80)
    
    # Setup plotting
    setup_plotting()
    
    # Run tests
    test_results = []
    
    # Test 1: Dimension validation
    hist, true, pred = test_dimension_validation()
    test_results.append(hist is not None)
    
    # Test 2: Standard visualization
    test_results.append(test_standard_visualization())
    
    # Test 3: Enhanced visualization
    test_results.append(test_enhanced_visualization())
    
    # Test 4: Edge cases
    test_edge_cases()
    
    # Summary
    print("\n" + "="*80)
    print("📊 Test Summary")
    print("="*80)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! TEFN visualization fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    print(f"\n📁 Check the 'plots' directory for generated visualization files.")

if __name__ == "__main__":
    main()
