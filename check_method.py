#!/usr/bin/env python3
"""
Check if _estimate_parameters method is properly defined in TransformerModel class
"""

def check_method_definition():
    """Check the method definition in the file."""
    try:
        # Read the file and check for the method
        with open('models/advanced_models/transformer_model.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Check if the method is defined
        if '_estimate_parameters' in content:
            print("✅ _estimate_parameters method found in file")
            
            # Find the method definition
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'def _estimate_parameters' in line:
                    print(f"   - Found at line {i+1}: {repr(line)}")
                    
                    # Check indentation of surrounding lines
                    start = max(0, i-2)
                    end = min(len(lines), i+5)
                    print("   - Context:")
                    for j in range(start, end):
                        marker = ">>>" if j == i else "   "
                        print(f"   {marker} {j+1:4d}: {repr(lines[j])}")
                    break
        else:
            print("❌ _estimate_parameters method not found in file")
        
        # Try to import and check the class
        print("\n🔍 Checking class definition...")
        import sys
        sys.path.append('.')
        
        from models.advanced_models.transformer_model import TransformerModel
        
        # Check if method exists in class
        if hasattr(TransformerModel, '_estimate_parameters'):
            print("✅ _estimate_parameters method found in class")
            method = getattr(TransformerModel, '_estimate_parameters')
            print(f"   - Method type: {type(method)}")
            print(f"   - Method: {method}")
        else:
            print("❌ _estimate_parameters method not found in class")
            print("   - Available methods:")
            methods = [attr for attr in dir(TransformerModel) if not attr.startswith('__')]
            for method in sorted(methods):
                if 'estimate' in method.lower() or 'param' in method.lower():
                    print(f"     - {method}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_method_definition()
