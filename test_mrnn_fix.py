#!/usr/bin/env python3
"""
Test script to verify the AttributeError fix in the optimized mRNN model.
This script tests that the model can be initialized and trained without errors.
"""

import torch
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mrnn_initialization():
    """Test that MRNNModel can be initialized with the new parameters."""
    print("🧪 Testing MRNNModel initialization...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Test with default parameters
        model1 = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=5,  # Small number for testing
            batch_size=16
        )
        print("✅ Default initialization successful")
        
        # Test with explicit optimization parameters
        model2 = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=5,
            batch_size=16,
            disable_checkpoint=False,  # Enable gradient checkpointing
            use_missing_embedding=True  # Enable missing value embeddings
        )
        print("✅ Optimized initialization successful")
        
        # Verify attributes exist
        assert hasattr(model2, 'disable_checkpoint'), "disable_checkpoint attribute missing"
        assert hasattr(model2, 'use_missing_embedding'), "use_missing_embedding attribute missing"
        print("✅ All required attributes present")
        
        return model2
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        raise

def test_mrnn_training():
    """Test that the optimized training method works without AttributeError."""
    print("\n🧪 Testing MRNNModel training...")
    
    try:
        # Initialize model
        model = test_mrnn_initialization()
        
        # Create small test dataset
        n_samples = 100
        sequence_len = 64
        n_features = 5
        
        # Generate synthetic data with missing values
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Create training data with missing values
        train_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        
        # Introduce missing values (NaN) - about 20% missing
        missing_mask = torch.rand(n_samples, sequence_len, n_features) < 0.2
        train_data[missing_mask] = float('nan')
        
        # Create ground truth (complete data)
        truth_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        
        print(f"📊 Test dataset:")
        print(f"   Shape: {train_data.shape}")
        print(f"   Missing values: {torch.isnan(train_data).sum().item()}")
        print(f"   Missing percentage: {torch.isnan(train_data).sum().item() / train_data.numel() * 100:.1f}%")
        
        # Test training with optimizations
        print("\n🚀 Starting optimized training...")
        model.fit(
            train_data=train_data,
            truth_data=truth_data,
            epochs=2,  # Very small for testing
            batch_size=16,
            patience=1,
            validation_split=0.2
        )
        
        print("✅ Training completed successfully!")
        
        # Test prediction
        print("\n🔮 Testing prediction...")
        test_input = train_data[:10]  # Use first 10 samples
        predictions = model.predict(test_input)
        print(f"✅ Prediction successful! Output shape: {predictions.shape}")
        
        return True
        
    except AttributeError as e:
        if 'disable_checkpoint' in str(e):
            print(f"❌ AttributeError still present: {e}")
            return False
        else:
            print(f"❌ Different AttributeError: {e}")
            raise
    except Exception as e:
        print(f"❌ Training failed with error: {e}")
        raise

def test_memory_optimizations():
    """Test that memory optimizations are properly configured."""
    print("\n🧪 Testing memory optimization settings...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Test with checkpointing enabled
        model_optimized = MRNNModel(
            n_features=5,
            sequence_len=64,
            disable_checkpoint=False,  # Enable checkpointing
            use_missing_embedding=True
        )
        
        # Test with checkpointing disabled
        model_standard = MRNNModel(
            n_features=5,
            sequence_len=64,
            disable_checkpoint=True,   # Disable checkpointing
            use_missing_embedding=False
        )
        
        print("✅ Both optimization configurations work")
        
        # Verify settings
        assert model_optimized.disable_checkpoint == False
        assert model_optimized.use_missing_embedding == True
        assert model_standard.disable_checkpoint == True
        assert model_standard.use_missing_embedding == False
        
        print("✅ Optimization settings correctly configured")
        return True
        
    except Exception as e:
        print(f"❌ Memory optimization test failed: {e}")
        raise

if __name__ == "__main__":
    print("🔧 Testing mRNN AttributeError Fix")
    print("=" * 50)
    
    try:
        # Run all tests
        test_mrnn_initialization()
        test_memory_optimizations()
        success = test_mrnn_training()
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 ALL TESTS PASSED!")
            print("✅ AttributeError has been successfully fixed")
            print("✅ Memory optimizations are working")
            print("✅ Training completes without errors")
        else:
            print("\n" + "=" * 50)
            print("❌ TESTS FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
