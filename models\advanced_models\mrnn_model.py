"""
Multi-Resolution RNN (mRNN) Model Implementation for Time Series Imputation
Custom multi-resolution RNN architecture with hierarchical processing for well log data

This module implements a multi-resolution RNN model with hierarchical feature extraction,
multi-scale temporal processing, and attention mechanisms for feature fusion.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
import warnings
import math

from .base_model import BaseAdvancedModel

class LearnableMissingEmbedding(nn.Module):
    """Learnable embedding for missing values instead of zero-filling."""

    def __init__(self, n_features: int, embedding_dim: int = 16):
        super().__init__()
        self.n_features = n_features
        self.embedding_dim = embedding_dim

        # Learnable embeddings for missing values per feature
        self.missing_embeddings = nn.Parameter(torch.randn(n_features, embedding_dim))
        self.projection = nn.Linear(embedding_dim, 1)

    def forward(self, x: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """
        Replace missing values with learnable embeddings.

        Args:
            x: Input tensor (batch, seq_len, n_features)
            mask: Boolean mask (True for observed, False for missing)

        Returns:
            Tensor with missing values replaced by learnable embeddings
        """
        batch_size, seq_len, n_features = x.shape

        # Create missing value replacements
        missing_values = torch.zeros_like(x)
        for feat_idx in range(n_features):
            # Get embedding for this feature
            feat_embedding = self.missing_embeddings[feat_idx]  # (embedding_dim,)
            # Project to scalar value
            missing_val = self.projection(feat_embedding)  # (1,)
            missing_values[:, :, feat_idx] = missing_val.squeeze()

        # Replace missing values
        result = torch.where(mask, x, missing_values)
        return result

class AttentionMechanism(nn.Module):
    """Attention mechanism for feature fusion."""
    
    def __init__(self, input_dim: int, attention_dim: int):
        super().__init__()
        self.attention_dim = attention_dim
        
        self.attention_layer = nn.Sequential(
            nn.Linear(input_dim, attention_dim),
            nn.Tanh(),
            nn.Linear(attention_dim, 1)
        )
        
    def forward(self, features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply attention mechanism to features.
        
        Args:
            features: Input features (batch, seq_len, feature_dim)
            
        Returns:
            Tuple of (attended_features, attention_weights)
        """
        # Calculate attention scores
        attention_scores = self.attention_layer(features)  # (batch, seq_len, 1)
        attention_weights = F.softmax(attention_scores, dim=1)  # (batch, seq_len, 1)
        
        # Apply attention weights
        attended_features = torch.sum(features * attention_weights, dim=1)  # (batch, feature_dim)
        
        return attended_features, attention_weights.squeeze(-1)

class MultiResolutionLSTM(nn.Module):
    """Multi-resolution LSTM layer with different temporal scales."""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], 
                 bidirectional: bool = True, dropout: float = 0.2):
        super().__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        self.bidirectional = bidirectional
        self.n_resolutions = len(hidden_sizes)
        
        # Create LSTM layers for different resolutions
        self.lstm_layers = nn.ModuleList()
        for hidden_size in hidden_sizes:
            lstm = nn.LSTM(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=1,
                batch_first=True,
                bidirectional=bidirectional,
                dropout=dropout if hidden_size > 1 else 0
            )
            self.lstm_layers.append(lstm)
        
        # Calculate output dimension
        self.output_dim = sum(hidden_sizes) * (2 if bidirectional else 1)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        Forward pass through multi-resolution LSTM.
        
        Args:
            x: Input tensor (batch, seq_len, input_size)
            
        Returns:
            Tuple of (concatenated_outputs, individual_outputs)
        """
        outputs = []
        hidden_states = []
        
        for i, lstm_layer in enumerate(self.lstm_layers):
            # Apply different downsampling for different resolutions
            if i > 0:
                # Downsample input for higher resolution levels
                stride = 2 ** i
                if x.size(1) >= stride:
                    x_downsampled = x[:, ::stride, :]
                else:
                    x_downsampled = x
            else:
                x_downsampled = x
            
            # LSTM forward pass
            lstm_out, (h_n, c_n) = lstm_layer(x_downsampled)
            
            # Upsample back to original sequence length if needed
            if lstm_out.size(1) != x.size(1):
                lstm_out = F.interpolate(
                    lstm_out.transpose(1, 2), 
                    size=x.size(1), 
                    mode='linear', 
                    align_corners=False
                ).transpose(1, 2)
            
            outputs.append(lstm_out)
            hidden_states.append(h_n)
        
        # Concatenate outputs from all resolutions
        concatenated_output = torch.cat(outputs, dim=-1)
        
        return concatenated_output, hidden_states

class MRNNNet(nn.Module):
    """Optimized Multi-Resolution RNN Network for time series imputation."""

    def __init__(self, n_features: int, sequence_len: int,
                 hidden_sizes: List[int] = [64, 128, 256],
                 n_layers: int = 3, bidirectional: bool = True,
                 attention_dim: int = 128, dropout: float = 0.2,
                 use_missing_embedding: bool = True):
        super().__init__()

        self.n_features = n_features
        self.sequence_len = sequence_len
        self.hidden_sizes = hidden_sizes
        self.n_layers = n_layers
        self.bidirectional = bidirectional
        self.attention_dim = attention_dim
        self.use_missing_embedding = use_missing_embedding

        # Missing value embedding
        if use_missing_embedding:
            self.missing_embedding = LearnableMissingEmbedding(n_features)

        # Input projection with residual connection
        self.input_projection = nn.Linear(n_features, hidden_sizes[0])
        self.input_norm = nn.LayerNorm(hidden_sizes[0])

        # Multi-resolution LSTM layers with pre-norm
        self.mrnn_layers = nn.ModuleList()
        self.pre_norms = nn.ModuleList()
        current_input_size = hidden_sizes[0]

        for _ in range(n_layers):
            # Pre-normalization
            self.pre_norms.append(nn.LayerNorm(current_input_size))

            mrnn_layer = MultiResolutionLSTM(
                input_size=current_input_size,
                hidden_sizes=hidden_sizes,
                bidirectional=bidirectional,
                dropout=dropout
            )
            self.mrnn_layers.append(mrnn_layer)
            current_input_size = mrnn_layer.output_dim

        # Attention mechanism for feature fusion
        self.attention = AttentionMechanism(current_input_size, attention_dim)

        # Optimized output layers with residual connections
        self.output_layers = nn.Sequential(
            nn.Linear(current_input_size, hidden_sizes[-1]),
            nn.LayerNorm(hidden_sizes[-1]),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_sizes[-1], n_features)
        )

        # Final layer normalization
        self.final_norm = nn.LayerNorm(current_input_size)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Optimized forward pass of the mRNN network.

        Args:
            x: Input tensor (batch, seq_len, n_features)
            mask: Optional mask for missing values (True for observed, False for missing)

        Returns:
            Output tensor (batch, seq_len, n_features)
        """
        # Handle missing values with learnable embeddings
        if self.use_missing_embedding and mask is not None:
            x = self.missing_embedding(x, mask)

        # Input projection with normalization
        x = self.input_projection(x)
        x = self.input_norm(x)

        # Multi-resolution LSTM layers with pre-normalization
        for i, (pre_norm, mrnn_layer) in enumerate(zip(self.pre_norms, self.mrnn_layers)):
            # Pre-normalization
            x_norm = pre_norm(x)

            # LSTM processing
            x_out, _ = mrnn_layer(x_norm)

            # Residual connection (if dimensions match)
            if x.shape[-1] == x_out.shape[-1]:
                x = x + x_out
            else:
                x = x_out

        # Final normalization
        x = self.final_norm(x)

        # Apply attention mechanism
        attended_features, _ = self.attention(x)

        # Expand attended features back to sequence length
        attended_features = attended_features.unsqueeze(1).expand(-1, self.sequence_len, -1)

        # Combine with original features (residual connection)
        combined_features = x + attended_features

        # Output projection
        output = self.output_layers(combined_features)

        return output

class MRNNModel(BaseAdvancedModel):
    """
    Multi-Resolution RNN model for well log imputation.
    
    Implements a multi-resolution RNN architecture with hierarchical processing,
    multi-scale temporal processing, and attention mechanisms for feature fusion.
    """
    
    def __init__(self, n_features: int = 4, sequence_len: int = 64,
                 hidden_sizes: List[int] = None, n_layers: int = 3,
                 bidirectional: bool = True, attention_dim: int = 128,
                 dropout: float = 0.2, epochs: int = 75, batch_size: int = 32,
                 learning_rate: float = 1e-3, disable_checkpoint: bool = False,
                 use_missing_embedding: bool = True, verbose: bool = True, **kwargs):
        """
        Initialize mRNN model for well log imputation.

        Args:
            n_features: Number of log features (GR, NPHI, RHOB, target)
            sequence_len: Length of input sequences (depth windows)
            hidden_sizes: List of hidden sizes for different resolutions
            n_layers: Number of resolution levels
            bidirectional: Whether to use bidirectional LSTM
            attention_dim: Attention mechanism dimension
            dropout: Dropout rate for regularization
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
            disable_checkpoint: Whether to disable gradient checkpointing (False = enabled)
            use_missing_embedding: Whether to use learnable missing value embeddings
            verbose: Whether to print training progress and debug information
            **kwargs: Additional model parameters
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, learning_rate, **kwargs)

        if hidden_sizes is None:
            hidden_sizes = [64, 128, 256]

        self.hidden_sizes = hidden_sizes
        self.n_layers = n_layers
        self.bidirectional = bidirectional
        self.attention_dim = attention_dim
        self.dropout = dropout
        self.disable_checkpoint = disable_checkpoint
        self.use_missing_embedding = use_missing_embedding
        self.verbose = verbose
        
        # Validate parameters
        self._validate_parameters()
        
        # Training components
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.criterion = nn.MSELoss()
        self.optimizer = None
        
        print(f"mRNN Model Configuration:")
        print(f"   - Hidden sizes: {hidden_sizes}")
        print(f"   - Resolution levels: {n_layers}")
        print(f"   - Bidirectional: {bidirectional}")
        print(f"   - Attention dim: {attention_dim}")
        print(f"   - Dropout rate: {dropout}")
        print(f"   - Gradient checkpointing: {'Disabled' if disable_checkpoint else 'Enabled'}")
        print(f"   - Missing value embeddings: {'Enabled' if use_missing_embedding else 'Disabled'}")
        print(f"   - Verbose output: {'Enabled' if verbose else 'Disabled'}")
        print(f"   - Device: {self.device}")
        
    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if len(self.hidden_sizes) < 1:
            raise ValueError("At least one hidden size must be specified")
        
        if self.n_layers < 1 or self.n_layers > 5:
            print(f"Warning: n_layers={self.n_layers} may not be optimal (recommended: 1-5)")
        
        if self.attention_dim < 32 or self.attention_dim > 512:
            print(f"Warning: attention_dim={self.attention_dim} may not be optimal (recommended: 32-512)")
        
        if self.n_features < 2:
            raise ValueError("mRNN requires at least 2 features for meaningful processing")
        
        if any(h < 16 or h > 1024 for h in self.hidden_sizes):
            print(f"Warning: Some hidden sizes may not be optimal (recommended: 16-1024)")
    
    def _initialize_model(self) -> None:
        """Initialize the mRNN model."""
        try:
            print(f"Initializing mRNN model...")
            
            self.model = MRNNNet(
                n_features=self.n_features,
                sequence_len=self.sequence_len,
                hidden_sizes=self.hidden_sizes,
                n_layers=self.n_layers,
                bidirectional=self.bidirectional,
                attention_dim=self.attention_dim,
                dropout=self.dropout,
                use_missing_embedding=self.use_missing_embedding
            ).to(self.device)
            
            # Initialize optimizer
            self.optimizer = torch.optim.Adam(
                self.model.parameters(),
                lr=self.learning_rate,
                weight_decay=1e-5
            )
            
            print(f"mRNN model initialized successfully")
            print(f"   - Parameters: ~{self._estimate_parameters():,}")
            print(f"   - Memory usage: ~{self._estimate_memory_mb():.1f} MB")
            
        except Exception as e:
            print(f"Failed to initialize mRNN model: {e}")
            raise RuntimeError(f"mRNN model initialization failed: {e}")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None,
                      keep_on_cpu: bool = False) -> Dict[str, Any]:
        """
        Prepare data for mRNN training/prediction.

        Args:
            data: Input data tensor
            truth_data: Ground truth data tensor (optional)
            keep_on_cpu: If True, keep tensors on CPU for DataLoader compatibility
        """
        # Convert to device only if not keeping on CPU
        if not keep_on_cpu:
            data = data.to(self.device)

        if truth_data is not None:
            if not keep_on_cpu:
                truth_data = truth_data.to(self.device)
            return {
                'input_data': data,
                'target_data': truth_data,
                'mask': ~torch.isnan(data)
            }
        else:
            return {
                'input_data': data,
                'mask': ~torch.isnan(data)
            }

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None,
            patience: int = 5, min_delta: float = 1e-4, validation_split: float = 0.2) -> None:
        """
        Train the mRNN model with enhanced optimizations and memory efficiency.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
            patience: Patience for early stopping (reduced default)
            min_delta: Minimum change for early stopping
            validation_split: Fraction of data to use for validation
        """
        import time
        from torch.optim.lr_scheduler import ReduceLROnPlateau
        from torch.utils.data import DataLoader, TensorDataset
        from torch.cuda.amp import autocast, GradScaler

        # Validate input data
        if not self._validate_input_data(train_data) or not self._validate_input_data(truth_data):
            raise ValueError("Invalid input data format")

        # Initialize model if not already done
        if self.model is None:
            self._initialize_model()

        # Use provided parameters or defaults
        epochs = epochs or self.epochs
        batch_size = batch_size or self.batch_size

        # Calculate optimal batch size using memory optimizer
        try:
            from utils.memory_optimization import get_optimal_batch_size
            model_params = {
                'd_model': max(self.hidden_sizes),
                'n_layers': self.n_layers,
                'n_heads': 4  # Approximate for attention
            }
            optimal_batch_size = get_optimal_batch_size(
                train_data.shape[0], train_data.shape[1], train_data.shape[2], model_params
            )
            if optimal_batch_size > batch_size:
                print(f"Increasing batch size from {batch_size} to {optimal_batch_size} for better efficiency")
                batch_size = optimal_batch_size
        except ImportError:
            print("Memory optimizer not available, using default batch size")

        print(f"Training optimized mRNN for {epochs} epochs...")
        print(f"   Training data shape: {train_data.shape}")
        print(f"   Missing values: {torch.isnan(train_data).sum().item()}")
        print(f"   Batch size: {batch_size}")
        print(f"   Validation split: {validation_split}")

        # Record start time
        start_time = time.time()

        # Prepare training data (keep on CPU for DataLoader compatibility)
        train_set = self._prepare_data(train_data, truth_data, keep_on_cpu=True)
        input_data = train_set['input_data']
        target_data = train_set['target_data']
        mask = train_set['mask']

        # Create train/validation split
        n_samples = input_data.size(0)
        n_val = int(n_samples * validation_split)
        n_train = n_samples - n_val

        # Split data (keep on CPU)
        train_input = input_data[:n_train]
        train_target = target_data[:n_train]
        train_mask = mask[:n_train]

        val_input = input_data[n_train:]
        val_target = target_data[n_train:]
        val_mask = mask[n_train:]

        # Determine pin_memory setting based on device availability and tensor location
        # Only use pin_memory if CUDA is available, model is on CUDA, and tensors are on CPU
        use_pin_memory = (torch.cuda.is_available() and
                         self.device.type == 'cuda' and
                         train_input.device.type == 'cpu')

        print(f"DataLoader Configuration:")
        print(f"   - CUDA available: {torch.cuda.is_available()}")
        print(f"   - Model device: {self.device}")
        print(f"   - Tensor device: {train_input.device}")
        print(f"   - Pin memory: {use_pin_memory}")

        # Create DataLoaders for efficient batching with error handling
        try:
            train_dataset = TensorDataset(train_input, train_target, train_mask)
            val_dataset = TensorDataset(val_input, val_target, val_mask)

            train_loader = DataLoader(
                train_dataset, batch_size=batch_size, shuffle=True,
                pin_memory=use_pin_memory, num_workers=0  # Set to 0 for Windows compatibility
            )
            val_loader = DataLoader(
                val_dataset, batch_size=batch_size, shuffle=False,
                pin_memory=use_pin_memory, num_workers=0
            )

            # Test the DataLoader by getting one batch
            test_batch = next(iter(train_loader))
            print(f"   - DataLoader test: ✓ (batch shape: {test_batch[0].shape})")

        except Exception as e:
            if "cannot pin" in str(e).lower():
                print(f"   Pin memory failed, retrying without pin_memory: {e}")
                # Fallback: disable pin_memory
                train_loader = DataLoader(
                    train_dataset, batch_size=batch_size, shuffle=True,
                    pin_memory=False, num_workers=0
                )
                val_loader = DataLoader(
                    val_dataset, batch_size=batch_size, shuffle=False,
                    pin_memory=False, num_workers=0
                )
                print(f"   - DataLoader fallback: ✓ (pin_memory disabled)")
            else:
                raise

        # Initialize mixed precision training
        scaler = GradScaler()

        # Learning rate scheduler
        scheduler = ReduceLROnPlateau(self.optimizer, mode='min', factor=0.5, patience=3, verbose=True)

        # Training loop setup
        self.model.train()
        self.training_history['loss'] = []
        self.training_history['val_loss'] = []
        best_val_loss = float('inf')
        early_stop_counter = 0

        print(f"Training optimizations enabled:")
        print(f"   - Mixed precision training: ✓")
        print(f"   - Gradient checkpointing: {'✓' if not self.disable_checkpoint else '✗'}")
        print(f"   - DataLoader batching: ✓")
        print(f"   - Validation monitoring: ✓")

        try:
            for epoch in range(epochs):
                epoch_start = time.time()

                # Training phase
                self.model.train()
                total_train_loss = 0.0
                n_train_batches = 0

                for batch_input, batch_target, batch_mask in train_loader:
                    # Move to device
                    batch_input = batch_input.to(self.device)
                    batch_target = batch_target.to(self.device)
                    batch_mask = batch_mask.to(self.device)

                    # Create missing value mask (True for observed, False for missing)
                    obs_mask = ~torch.isnan(batch_input)

                    # Zero gradients
                    self.optimizer.zero_grad()

                    # Mixed precision forward pass
                    with autocast():
                        # Forward pass with optional gradient checkpointing
                        if not self.disable_checkpoint and batch_input.size(0) > 1:
                            # Use gradient checkpointing for memory efficiency
                            from torch.utils.checkpoint import checkpoint
                            def checkpoint_fn(x, mask):
                                return self.model(x, mask)
                            predictions = checkpoint(checkpoint_fn, batch_input, obs_mask, use_reentrant=False)
                        else:
                            # Standard forward pass
                            predictions = self.model(batch_input, obs_mask)

                        # Calculate loss only on observed values
                        valid_mask = batch_mask & obs_mask
                        if valid_mask.any():
                            loss = self.criterion(predictions[valid_mask], batch_target[valid_mask])
                        else:
                            continue  # Skip batch if no valid values

                    # Diagnostic output for first batch
                    if epoch == 0 and n_train_batches == 0:
                        print(f"Training Diagnostics:")
                        print(f"   Batch shape: {batch_input.shape}")
                        print(f"   Missing values in batch: {torch.isnan(batch_input).sum().item()}")
                        print(f"   Valid predictions: {valid_mask.sum().item()}")
                        print(f"   Loss: {loss.item():.6f}")

                    # Backward pass with mixed precision
                    scaler.scale(loss).backward()

                    # Gradient clipping
                    scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                    # Optimizer step
                    scaler.step(self.optimizer)
                    scaler.update()

                    total_train_loss += loss.item()
                    n_train_batches += 1

                # Validation phase
                self.model.eval()
                total_val_loss = 0.0
                n_val_batches = 0

                with torch.no_grad():
                    for batch_input, batch_target, batch_mask in val_loader:
                        batch_input = batch_input.to(self.device)
                        batch_target = batch_target.to(self.device)
                        batch_mask = batch_mask.to(self.device)

                        obs_mask = ~torch.isnan(batch_input)

                        with autocast():
                            # Forward pass (no checkpointing needed in validation)
                            predictions = self.model(batch_input, obs_mask)
                            valid_mask = batch_mask & obs_mask

                            if valid_mask.any():
                                val_loss = self.criterion(predictions[valid_mask], batch_target[valid_mask])
                                total_val_loss += val_loss.item()
                                n_val_batches += 1

                # Calculate average losses
                avg_train_loss = total_train_loss / max(n_train_batches, 1)
                avg_val_loss = total_val_loss / max(n_val_batches, 1)

                self.training_history['loss'].append(avg_train_loss)
                self.training_history['val_loss'].append(avg_val_loss)

                # Learning rate scheduling
                scheduler.step(avg_val_loss)

                # Early stopping check
                if avg_val_loss < best_val_loss - min_delta:
                    best_val_loss = avg_val_loss
                    early_stop_counter = 0
                    # Save best model
                    self.best_model_state = self.model.state_dict().copy()
                else:
                    early_stop_counter += 1

                # Print progress
                epoch_time = time.time() - epoch_start
                if self.verbose and (epoch + 1) % 5 == 0:
                    print(f"Epoch {epoch+1}/{epochs} - Train Loss: {avg_train_loss:.6f} - Val Loss: {avg_val_loss:.6f} - Time: {epoch_time:.2f}s")

                # Early stopping
                if early_stop_counter >= patience:
                    print(f"Early stopping at epoch {epoch+1} (best val loss: {best_val_loss:.6f})")
                    break

            self.is_fitted = True

            # Record training time
            training_time = time.time() - start_time
            self.training_history['total_training_time'] = training_time

            print(f"mRNN training completed in {training_time:.2f} seconds!")
            print(f"   Final loss: {self.training_history['loss'][-1]:.6f}")

        except Exception as e:
            print(f"Training failed: {e}")
            raise

    def _calculate_masked_loss(self, predictions: torch.Tensor, targets: torch.Tensor,
                              mask: torch.Tensor) -> torch.Tensor:
        """Calculate loss only on observed values."""
        # Apply mask to both predictions and targets
        masked_predictions = predictions[mask]
        masked_targets = targets[mask]

        if masked_predictions.numel() == 0:
            # If no observed values, return zero loss
            return torch.tensor(0.0, device=predictions.device, requires_grad=True)

        return self.criterion(masked_predictions, masked_targets)

    def predict(self, data: torch.Tensor) -> torch.Tensor:
        """
        Predict/impute missing values using the mRNN.

        Args:
            data: Input data with missing values

        Returns:
            Imputed data tensor
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before prediction")

        # Validate input data
        if not self._validate_input_data(data):
            raise ValueError("Invalid input data format")

        print(f"Predicting with mRNN...")
        print(f"   Input data shape: {data.shape}")

        # Prepare test data
        test_set = self._prepare_data(data)
        input_data = test_set['input_data']
        mask = test_set['mask']

        # Replace NaN values with zeros for prediction
        input_data_clean = torch.where(torch.isnan(input_data), torch.zeros_like(input_data), input_data)

        # Prediction
        self.model.eval()
        with torch.no_grad():
            predictions = self.model(input_data_clean)

            # Combine original observed values with predictions for missing values
            result = torch.where(mask, input_data, predictions)

        print(f"Prediction completed")
        return result.cpu()

    def _estimate_parameters(self) -> int:
        """Estimate the number of model parameters."""
        if self.model is not None:
            return sum(p.numel() for p in self.model.parameters())

        # Rough estimation for mRNN model
        total_params = 0

        # Input projection
        total_params += self.n_features * self.hidden_sizes[0]

        # Multi-resolution LSTM layers
        for layer_idx in range(self.n_layers):
            for hidden_size in self.hidden_sizes:
                # LSTM parameters (input_size, hidden_size, bidirectional)
                input_size = self.hidden_sizes[0] if layer_idx == 0 else sum(self.hidden_sizes) * (2 if self.bidirectional else 1)
                lstm_params = 4 * (input_size + hidden_size + 1) * hidden_size  # 4 gates
                if self.bidirectional:
                    lstm_params *= 2
                total_params += lstm_params

        # Attention mechanism
        final_hidden_size = sum(self.hidden_sizes) * (2 if self.bidirectional else 1)
        attention_params = (
            final_hidden_size * self.attention_dim +
            self.attention_dim * 1
        )
        total_params += attention_params

        # Output layers
        output_params = (
            final_hidden_size * self.hidden_sizes[-1] +
            self.hidden_sizes[-1] * self.hidden_sizes[0] +
            self.hidden_sizes[0] * self.n_features
        )
        total_params += output_params

        return total_params

    def _estimate_memory_mb(self) -> float:
        """Estimate memory usage in MB."""
        # Rough estimation based on model size and batch size
        param_memory = self._estimate_parameters() * 4 / (1024 * 1024)  # 4 bytes per float32

        # Activation memory for multi-resolution processing
        max_hidden_size = max(self.hidden_sizes)
        activation_memory = (
            self.batch_size * self.sequence_len * max_hidden_size *
            self.n_layers * 4 / (1024 * 1024)  # 4 for different LSTM gates
        )

        return param_memory + activation_memory

    def get_attention_weights(self, data: torch.Tensor) -> Optional[np.ndarray]:
        """
        Extract attention weights for visualization.

        Args:
            data: Input data tensor

        Returns:
            Attention weights array or None if not available
        """
        if not self.is_fitted:
            print("Model must be fitted before extracting attention weights")
            return None

        try:
            # This would require modification to store attention weights during forward pass
            # For now, return None and implement in future versions
            print("ℹ️ Attention weight extraction not yet implemented")
            return None
        except Exception as e:
            print(f"Failed to extract attention weights: {e}")
            return None

    def get_model_complexity(self) -> Dict[str, Any]:
        """Get model complexity metrics."""
        return {
            'total_parameters': self._estimate_parameters(),
            'hidden_sizes': self.hidden_sizes,
            'resolution_levels': self.n_layers,
            'bidirectional': self.bidirectional,
            'attention_dimension': self.attention_dim,
            'complexity_score': 3,  # High complexity
            'memory_mb': self._estimate_memory_mb(),
            'computational_cost': 'medium',
            'performance_tier': 'high'
        }
