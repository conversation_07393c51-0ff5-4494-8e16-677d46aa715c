"""
Mixed Precision Training Utilities for PyPOTS Models

This module provides robust utilities for handling mixed precision training
with proper gradient scaling, error handling, and memory optimization.
"""

import torch
import torch.nn as nn
from typing import Optional, Dict, Any, Callable, Tuple
import warnings


class RobustGradScaler:
    """
    A robust wrapper around PyTorch's GradScaler that properly handles
    gradient errors and maintains scaler state consistency.
    """
    
    def __init__(self, device: str = 'cuda', **kwargs):
        """
        Initialize the robust gradient scaler.
        
        Args:
            device: Target device ('cuda' or 'cpu')
            **kwargs: Additional arguments for GradScaler
        """
        self.device = device
        self.scaler = None
        self.enabled = False
        
        if device == 'cuda' and torch.cuda.is_available():
            try:
                # Try new API first (PyTorch 2.0+)
                self.scaler = torch.amp.GradScaler('cuda', **kwargs)
                self.enabled = True
            except AttributeError:
                try:
                    # Fallback to old API
                    self.scaler = torch.cuda.amp.GradScaler(**kwargs)
                    self.enabled = True
                except Exception as e:
                    warnings.warn(f"Failed to create GradScaler: {e}")
                    self.enabled = False
    
    def scale(self, loss: torch.Tensor) -> torch.Tensor:
        """Scale the loss tensor."""
        if self.enabled and self.scaler is not None:
            return self.scaler.scale(loss)
        return loss
    
    def backward(self, loss: torch.Tensor, **kwargs) -> None:
        """Perform backward pass with optional scaling."""
        if self.enabled and self.scaler is not None:
            self.scaler.scale(loss).backward(**kwargs)
        else:
            loss.backward(**kwargs)
    
    def step_and_update(self, optimizer: torch.optim.Optimizer, 
                       model: nn.Module, 
                       max_grad_norm: float = 1.0,
                       skip_on_nan: bool = True) -> Dict[str, Any]:
        """
        Perform optimizer step with proper gradient handling.
        
        Args:
            optimizer: PyTorch optimizer
            model: Model to update
            max_grad_norm: Maximum gradient norm for clipping
            skip_on_nan: Whether to skip updates on NaN/inf gradients
            
        Returns:
            Dictionary with step information
        """
        step_info = {
            'step_taken': False,
            'gradients_finite': True,
            'gradients_exist': True,
            'grad_norm': 0.0,
            'scale_factor': 1.0
        }
        
        if not self.enabled or self.scaler is None:
            return self._standard_step(optimizer, model, max_grad_norm, step_info)
        
        # Check for gradients before unscaling
        has_gradients = any(p.grad is not None for p in model.parameters() if p.requires_grad)
        step_info['gradients_exist'] = has_gradients
        
        if not has_gradients:
            optimizer.zero_grad()
            self.scaler.update()
            return step_info
        
        # Unscale gradients for inspection and clipping
        self.scaler.unscale_(optimizer)
        
        # Check for finite gradients
        finite_gradients = self._check_finite_gradients(model)
        step_info['gradients_finite'] = finite_gradients
        
        if finite_gradients:
            # Calculate gradient norm before clipping
            total_norm = 0.0
            for p in model.parameters():
                if p.requires_grad and p.grad is not None:
                    param_norm = p.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
            step_info['grad_norm'] = total_norm ** 0.5
            
            # Apply gradient clipping
            if max_grad_norm > 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=max_grad_norm)
        
        # Always call step and update to maintain scaler state
        self.scaler.step(optimizer)
        self.scaler.update()
        
        # Record scale factor
        step_info['scale_factor'] = self.scaler.get_scale()
        step_info['step_taken'] = finite_gradients
        
        return step_info
    
    def _standard_step(self, optimizer: torch.optim.Optimizer, 
                      model: nn.Module, 
                      max_grad_norm: float,
                      step_info: Dict[str, Any]) -> Dict[str, Any]:
        """Handle standard precision training step."""
        # Check for gradients
        has_gradients = any(p.grad is not None for p in model.parameters() if p.requires_grad)
        step_info['gradients_exist'] = has_gradients
        
        if not has_gradients:
            optimizer.zero_grad()
            return step_info
        
        # Check for finite gradients
        finite_gradients = self._check_finite_gradients(model)
        step_info['gradients_finite'] = finite_gradients
        
        if finite_gradients:
            # Calculate gradient norm
            total_norm = 0.0
            for p in model.parameters():
                if p.requires_grad and p.grad is not None:
                    param_norm = p.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
            step_info['grad_norm'] = total_norm ** 0.5
            
            # Apply gradient clipping
            if max_grad_norm > 0:
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=max_grad_norm)
            
            optimizer.step()
            step_info['step_taken'] = True
        else:
            optimizer.zero_grad()
        
        return step_info
    
    def _check_finite_gradients(self, model: nn.Module) -> bool:
        """Check if all gradients are finite."""
        for p in model.parameters():
            if p.requires_grad and p.grad is not None:
                if not torch.isfinite(p.grad).all():
                    return False
        return True
    
    def get_scale(self) -> float:
        """Get current scale factor."""
        if self.enabled and self.scaler is not None:
            return self.scaler.get_scale()
        return 1.0
    
    def state_dict(self) -> Dict[str, Any]:
        """Get scaler state dict."""
        if self.enabled and self.scaler is not None:
            return self.scaler.state_dict()
        return {}
    
    def load_state_dict(self, state_dict: Dict[str, Any]) -> None:
        """Load scaler state dict."""
        if self.enabled and self.scaler is not None:
            self.scaler.load_state_dict(state_dict)


def create_autocast_context(device: str = 'cuda', **kwargs):
    """
    Create appropriate autocast context for the device.
    
    Args:
        device: Target device
        **kwargs: Additional arguments for autocast
        
    Returns:
        Autocast context manager or dummy context
    """
    if device == 'cuda' and torch.cuda.is_available():
        try:
            # Try new API first
            return torch.amp.autocast('cuda', **kwargs)
        except AttributeError:
            try:
                # Fallback to old API
                return torch.cuda.amp.autocast(**kwargs)
            except Exception:
                pass
    
    # Return dummy context for CPU or when CUDA is not available
    from contextlib import nullcontext
    return nullcontext()


class MixedPrecisionTrainer:
    """
    A comprehensive mixed precision training helper for time series models.
    """
    
    def __init__(self, model: nn.Module, optimizer: torch.optim.Optimizer,
                 device: str = 'cuda', max_grad_norm: float = 1.0,
                 enable_mixed_precision: bool = True):
        """
        Initialize the mixed precision trainer.
        
        Args:
            model: PyTorch model
            optimizer: PyTorch optimizer
            device: Target device
            max_grad_norm: Maximum gradient norm for clipping
            enable_mixed_precision: Whether to enable mixed precision
        """
        self.model = model
        self.optimizer = optimizer
        self.device = device
        self.max_grad_norm = max_grad_norm
        
        # Initialize scaler and autocast context
        self.scaler = RobustGradScaler(device) if enable_mixed_precision else None
        self.autocast_context = create_autocast_context(device) if enable_mixed_precision else None
        self.mixed_precision_enabled = (self.scaler is not None and self.scaler.enabled)
        
        # Training statistics
        self.stats = {
            'total_steps': 0,
            'successful_steps': 0,
            'skipped_steps': 0,
            'nan_gradients': 0,
            'no_gradients': 0,
            'avg_grad_norm': 0.0,
            'scale_factor_history': []
        }
    
    def training_step(self, loss_fn: Callable, *args, **kwargs) -> Dict[str, Any]:
        """
        Perform a complete training step with proper error handling.
        
        Args:
            loss_fn: Function that computes the loss
            *args, **kwargs: Arguments for the loss function
            
        Returns:
            Dictionary with step results
        """
        self.optimizer.zero_grad()
        
        # Forward pass with optional autocast
        if self.mixed_precision_enabled and self.autocast_context is not None:
            with self.autocast_context:
                loss = loss_fn(*args, **kwargs)
        else:
            loss = loss_fn(*args, **kwargs)
        
        # Validate loss
        if not isinstance(loss, torch.Tensor) or not loss.requires_grad:
            return {'success': False, 'error': 'Invalid loss tensor'}
        
        if not torch.isfinite(loss):
            return {'success': False, 'error': 'Non-finite loss'}
        
        # Backward pass
        if self.scaler is not None:
            self.scaler.backward(loss)
        else:
            loss.backward()
        
        # Optimizer step with error handling
        if self.scaler is not None:
            step_info = self.scaler.step_and_update(
                self.optimizer, self.model, self.max_grad_norm
            )
        else:
            step_info = self.scaler._standard_step(
                self.optimizer, self.model, self.max_grad_norm, {}
            )
        
        # Update statistics
        self._update_stats(step_info)
        
        return {
            'success': True,
            'loss': loss.item(),
            'step_taken': step_info['step_taken'],
            'grad_norm': step_info['grad_norm'],
            'scale_factor': step_info.get('scale_factor', 1.0)
        }
    
    def _update_stats(self, step_info: Dict[str, Any]) -> None:
        """Update training statistics."""
        self.stats['total_steps'] += 1
        
        if step_info['step_taken']:
            self.stats['successful_steps'] += 1
            # Update running average of gradient norm
            alpha = 0.1  # Exponential moving average factor
            self.stats['avg_grad_norm'] = (
                alpha * step_info['grad_norm'] + 
                (1 - alpha) * self.stats['avg_grad_norm']
            )
        else:
            self.stats['skipped_steps'] += 1
            if not step_info['gradients_finite']:
                self.stats['nan_gradients'] += 1
            if not step_info['gradients_exist']:
                self.stats['no_gradients'] += 1
        
        if 'scale_factor' in step_info:
            self.stats['scale_factor_history'].append(step_info['scale_factor'])
            # Keep only last 100 scale factors
            if len(self.stats['scale_factor_history']) > 100:
                self.stats['scale_factor_history'] = self.stats['scale_factor_history'][-100:]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get training statistics."""
        stats = self.stats.copy()
        if stats['total_steps'] > 0:
            stats['success_rate'] = stats['successful_steps'] / stats['total_steps']
            stats['skip_rate'] = stats['skipped_steps'] / stats['total_steps']
        else:
            stats['success_rate'] = 0.0
            stats['skip_rate'] = 0.0
        
        return stats
    
    def print_stats(self) -> None:
        """Print training statistics."""
        stats = self.get_stats()
        print(f"\n📊 Mixed Precision Training Statistics:")
        print(f"   Total steps: {stats['total_steps']}")
        print(f"   Successful steps: {stats['successful_steps']} ({stats['success_rate']:.1%})")
        print(f"   Skipped steps: {stats['skipped_steps']} ({stats['skip_rate']:.1%})")
        print(f"   NaN gradients: {stats['nan_gradients']}")
        print(f"   No gradients: {stats['no_gradients']}")
        print(f"   Avg gradient norm: {stats['avg_grad_norm']:.4f}")
        
        if self.mixed_precision_enabled and stats['scale_factor_history']:
            current_scale = stats['scale_factor_history'][-1]
            print(f"   Current scale factor: {current_scale:.0f}")
