#!/usr/bin/env python3
"""
Display Utilities for Cross-Platform Emoji and Font Management

This module provides utilities for handling emoji display, font management,
and cross-platform compatibility in matplotlib and console output.

Author: ML Log Prediction System
Date: 2024
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
import warnings
import logging
from typing import Dict, List, Optional, Tuple, Union
import os

# Configure logging
logger = logging.getLogger(__name__)

class DisplayManager:
    """
    Manages display settings, emoji rendering, and font configuration
    for cross-platform compatibility.
    """
    
    # Emoji and fallback symbol mappings
    RANKING_SYMBOLS = {
        'emoji': {
            1: '🥇',
            2: '🥈', 
            3: '🥉',
            'default': lambda x: f'{x}.'
        },
        'text': {
            1: '[1st]',
            2: '[2nd]',
            3: '[3rd]',
            'default': lambda x: f'[{x}]'
        },
        'unicode': {
            1: '①',
            2: '②',
            3: '③',
            'default': lambda x: f'{x}.'
        }
    }
    
    # Font preferences by platform
    EMOJI_FONTS = {
        'Windows': [
            'Segoe UI Emoji',
            'Microsoft YaHei',
            'Segoe UI',
            'Arial Unicode MS'
        ],
        'Darwin': [  # macOS
            'Apple Color Emoji',
            'Helvetica Neue',
            'Arial Unicode MS'
        ],
        'Linux': [
            'Noto Color Emoji',
            'Noto Emoji',
            'DejaVu Sans',
            'Liberation Sans'
        ]
    }
    
    def __init__(self, 
                 prefer_emoji: bool = True,
                 fallback_mode: str = 'text',
                 suppress_warnings: bool = True,
                 auto_configure_fonts: bool = True):
        """
        Initialize the Display Manager.
        
        Parameters:
        -----------
        prefer_emoji : bool
            Whether to prefer emoji symbols over text alternatives
        fallback_mode : str
            Fallback mode when emojis aren't available ('text', 'unicode', 'simple')
        suppress_warnings : bool
            Whether to suppress matplotlib font warnings
        auto_configure_fonts : bool
            Whether to automatically configure matplotlib fonts
        """
        self.prefer_emoji = prefer_emoji
        self.fallback_mode = fallback_mode
        self.suppress_warnings = suppress_warnings
        self.emoji_support = None
        self.current_font = None
        
        # Initialize font configuration
        if auto_configure_fonts:
            self.configure_matplotlib_fonts()
        
        # Test emoji support
        self.test_emoji_support()
        
        # Configure warning handling
        if suppress_warnings:
            self.configure_warning_filters()
    
    def configure_matplotlib_fonts(self) -> bool:
        """
        Configure matplotlib to use fonts that support emojis.
        
        Returns:
        --------
        bool : True if emoji-capable font was found and set
        """
        system = platform.system()
        font_candidates = self.EMOJI_FONTS.get(system, self.EMOJI_FONTS['Linux'])
        
        logger.info(f"Configuring fonts for {system} system")
        
        # Find available fonts
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        
        # Try to find and set an emoji-capable font
        for font_name in font_candidates:
            if font_name in available_fonts:
                try:
                    plt.rcParams['font.family'] = font_name
                    self.current_font = font_name
                    logger.info(f"Set matplotlib font to: {font_name}")
                    return True
                except Exception as e:
                    logger.warning(f"Failed to set font {font_name}: {e}")
                    continue
        
        # Fallback to default with emoji support attempt
        try:
            if system == 'Windows':
                plt.rcParams['font.family'] = ['Segoe UI', 'DejaVu Sans']
            elif system == 'Darwin':
                plt.rcParams['font.family'] = ['Helvetica Neue', 'DejaVu Sans']
            else:
                plt.rcParams['font.family'] = ['DejaVu Sans', 'Liberation Sans']
            
            logger.info("Set fallback font configuration")
            return False
        except Exception as e:
            logger.error(f"Failed to configure fallback fonts: {e}")
            return False
    
    def test_emoji_support(self) -> bool:
        """
        Test if the current matplotlib configuration supports emoji rendering.
        
        Returns:
        --------
        bool : True if emoji support is available
        """
        try:
            # Create a test figure to check emoji rendering
            fig, ax = plt.subplots(figsize=(1, 1))
            
            # Try to render a test emoji
            with warnings.catch_warnings():
                warnings.simplefilter("error")
                ax.text(0.5, 0.5, '🥇', fontsize=12, ha='center', va='center')
                plt.close(fig)
                
            self.emoji_support = True
            logger.info("Emoji support confirmed")
            return True
            
        except (UserWarning, Exception) as e:
            self.emoji_support = False
            logger.warning(f"Emoji support not available: {e}")
            plt.close('all')  # Clean up any open figures
            return False
    
    def configure_warning_filters(self):
        """Configure warning filters for font-related warnings."""
        # Filter out specific glyph warnings
        warnings.filterwarnings(
            'ignore',
            message=r'Glyph .* missing from font.*',
            category=UserWarning
        )
        
        # Filter out tkinter font warnings
        warnings.filterwarnings(
            'ignore',
            message=r'Glyph .* missing from font.*',
            module='tkinter'
        )
        
        logger.info("Configured warning filters for font issues")
    
    def get_ranking_symbol(self, rank: int, mode: Optional[str] = None) -> str:
        """
        Get appropriate ranking symbol based on configuration and availability.
        
        Parameters:
        -----------
        rank : int
            The ranking position (1, 2, 3, etc.)
        mode : str, optional
            Override mode ('emoji', 'text', 'unicode')
            
        Returns:
        --------
        str : Appropriate symbol for the rank
        """
        # Determine which mode to use
        if mode is None:
            if self.prefer_emoji and self.emoji_support:
                mode = 'emoji'
            else:
                mode = self.fallback_mode
        
        # Get symbol set
        symbol_set = self.RANKING_SYMBOLS.get(mode, self.RANKING_SYMBOLS['text'])
        
        # Return appropriate symbol
        if rank in symbol_set:
            return symbol_set[rank]
        else:
            return symbol_set['default'](rank)
    
    def format_ranking_text(self, 
                           rank: int, 
                           model_name: str, 
                           metrics: Dict[str, float],
                           mode: Optional[str] = None) -> str:
        """
        Format a complete ranking text line.
        
        Parameters:
        -----------
        rank : int
            Ranking position
        model_name : str
            Name of the model
        metrics : dict
            Dictionary of metrics (r2, mae, etc.)
        mode : str, optional
            Symbol mode override
            
        Returns:
        --------
        str : Formatted ranking text
        """
        symbol = self.get_ranking_symbol(rank, mode)
        
        # Format metrics
        metric_parts = []
        for key, value in metrics.items():
            if key.lower() == 'r2':
                metric_parts.append(f"R²: {value:.3f}")
            elif key.lower() == 'mae':
                metric_parts.append(f"MAE: {value:.3f}")
            elif key.lower() in ['score', 'composite_score']:
                metric_parts.append(f"Score: {value:.3f}")
            else:
                metric_parts.append(f"{key}: {value:.3f}")
        
        metrics_text = " | ".join(metric_parts)
        
        return f"{symbol} {model_name:<15} | {metrics_text}"
    
    def add_ranking_annotation(self, 
                              ax, 
                              rank: int, 
                              x: float, 
                              y: float,
                              mode: Optional[str] = None,
                              **kwargs) -> None:
        """
        Add ranking annotation to matplotlib axes.
        
        Parameters:
        -----------
        ax : matplotlib.axes.Axes
            The axes to add annotation to
        rank : int
            Ranking position
        x, y : float
            Position coordinates
        mode : str, optional
            Symbol mode override
        **kwargs : additional arguments for ax.text()
        """
        symbol = self.get_ranking_symbol(rank, mode)
        
        # Default text properties
        text_props = {
            'fontsize': 12,
            'ha': 'center',
            'va': 'center',
            'fontweight': 'bold'
        }
        text_props.update(kwargs)
        
        try:
            ax.text(x, y, symbol, **text_props)
        except Exception as e:
            # Fallback to text mode if emoji fails
            if mode != 'text':
                fallback_symbol = self.get_ranking_symbol(rank, 'text')
                ax.text(x, y, fallback_symbol, **text_props)
                logger.warning(f"Emoji annotation failed, used text fallback: {e}")
            else:
                logger.error(f"Text annotation failed: {e}")
    
    def print_ranking_summary(self, 
                             rankings: List[Dict],
                             title: str = "MODEL RANKING SUMMARY",
                             mode: Optional[str] = None) -> None:
        """
        Print a formatted ranking summary to console.
        
        Parameters:
        -----------
        rankings : list of dict
            List of ranking data with 'rank', 'model_name', and metrics
        title : str
            Title for the summary
        mode : str, optional
            Symbol mode override
        """
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
        
        for ranking in rankings:
            rank = ranking.get('rank', 0)
            model_name = ranking.get('model_name', 'Unknown')
            
            # Extract metrics
            metrics = {}
            for key, value in ranking.items():
                if key not in ['rank', 'model_name'] and isinstance(value, (int, float)):
                    metrics[key] = value
            
            formatted_line = self.format_ranking_text(rank, model_name, metrics, mode)
            print(formatted_line)
        
        print("="*60)
    
    def get_font_info(self) -> Dict[str, Union[str, bool, List[str]]]:
        """
        Get information about current font configuration.
        
        Returns:
        --------
        dict : Font configuration information
        """
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        system = platform.system()
        
        return {
            'system': system,
            'current_font': self.current_font or plt.rcParams.get('font.family', 'Unknown'),
            'emoji_support': self.emoji_support,
            'available_emoji_fonts': [
                font for font in self.EMOJI_FONTS.get(system, [])
                if font in available_fonts
            ],
            'total_available_fonts': len(available_fonts)
        }
    
    def diagnose_display_issues(self) -> Dict[str, Union[str, bool, List[str]]]:
        """
        Diagnose potential display issues and provide recommendations.
        
        Returns:
        --------
        dict : Diagnostic information and recommendations
        """
        font_info = self.get_font_info()
        issues = []
        recommendations = []
        
        # Check emoji support
        if not self.emoji_support:
            issues.append("Emoji rendering not supported with current font")
            recommendations.append("Install emoji-capable fonts or use text fallback mode")
        
        # Check available emoji fonts
        if not font_info['available_emoji_fonts']:
            issues.append("No emoji-capable fonts detected")
            system = font_info['system']
            if system == 'Windows':
                recommendations.append("Install Segoe UI Emoji or Microsoft YaHei")
            elif system == 'Darwin':
                recommendations.append("Ensure Apple Color Emoji is available")
            else:
                recommendations.append("Install Noto Color Emoji or Noto Emoji")
        
        # Check matplotlib configuration
        current_font = font_info['current_font']
        if isinstance(current_font, list):
            current_font = current_font[0] if current_font else 'Unknown'
        
        if current_font == 'DejaVu Sans' and self.prefer_emoji:
            issues.append("Using DejaVu Sans which has limited emoji support")
            recommendations.append("Configure matplotlib to use emoji-capable font")
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'font_info': font_info,
            'emoji_support': self.emoji_support
        }


# Global display manager instance
_display_manager = None

def get_display_manager(**kwargs) -> DisplayManager:
    """
    Get or create the global display manager instance.
    
    Parameters:
    -----------
    **kwargs : keyword arguments for DisplayManager initialization
    
    Returns:
    --------
    DisplayManager : The global display manager instance
    """
    global _display_manager
    if _display_manager is None:
        _display_manager = DisplayManager(**kwargs)
    return _display_manager

def configure_display(prefer_emoji: bool = True,
                     fallback_mode: str = 'text',
                     suppress_warnings: bool = True) -> DisplayManager:
    """
    Configure the global display manager.
    
    Parameters:
    -----------
    prefer_emoji : bool
        Whether to prefer emoji symbols
    fallback_mode : str
        Fallback mode for symbols
    suppress_warnings : bool
        Whether to suppress font warnings
        
    Returns:
    --------
    DisplayManager : Configured display manager
    """
    global _display_manager
    _display_manager = DisplayManager(
        prefer_emoji=prefer_emoji,
        fallback_mode=fallback_mode,
        suppress_warnings=suppress_warnings
    )
    return _display_manager

# Convenience functions
def get_ranking_symbol(rank: int, mode: Optional[str] = None) -> str:
    """Get ranking symbol using global display manager."""
    return get_display_manager().get_ranking_symbol(rank, mode)

def format_ranking_text(rank: int, model_name: str, metrics: Dict[str, float], mode: Optional[str] = None) -> str:
    """Format ranking text using global display manager."""
    return get_display_manager().format_ranking_text(rank, model_name, metrics, mode)

def add_ranking_annotation(ax, rank: int, x: float, y: float, **kwargs) -> None:
    """Add ranking annotation using global display manager."""
    return get_display_manager().add_ranking_annotation(ax, rank, x, y, **kwargs)

def print_ranking_summary(rankings: List[Dict], title: str = "MODEL RANKING SUMMARY") -> None:
    """Print ranking summary using global display manager."""
    return get_display_manager().print_ranking_summary(rankings, title)

def diagnose_display() -> Dict:
    """Diagnose display issues using global display manager."""
    return get_display_manager().diagnose_display_issues()


if __name__ == "__main__":
    # Test the display manager
    print("Testing Display Manager...")
    
    # Initialize display manager
    dm = DisplayManager()
    
    # Test ranking symbols
    print("\nTesting ranking symbols:")
    for i in range(1, 6):
        emoji_symbol = dm.get_ranking_symbol(i, 'emoji')
        text_symbol = dm.get_ranking_symbol(i, 'text')
        unicode_symbol = dm.get_ranking_symbol(i, 'unicode')
        print(f"Rank {i}: Emoji='{emoji_symbol}' Text='{text_symbol}' Unicode='{unicode_symbol}'")
    
    # Test ranking text formatting
    print("\nTesting ranking text formatting:")
    test_rankings = [
        {'rank': 1, 'model_name': 'SAITS', 'r2': 0.95, 'mae': 0.12},
        {'rank': 2, 'model_name': 'BRITS', 'r2': 0.92, 'mae': 0.15},
        {'rank': 3, 'model_name': 'Transformer', 'r2': 0.89, 'mae': 0.18}
    ]
    
    dm.print_ranking_summary(test_rankings)
    
    # Diagnose display issues
    print("\nDisplay Diagnostics:")
    diagnostics = dm.diagnose_display_issues()
    
    print(f"Emoji Support: {diagnostics['emoji_support']}")
    print(f"Issues: {diagnostics['issues']}")
    print(f"Recommendations: {diagnostics['recommendations']}")
    
    font_info = diagnostics['font_info']
    print(f"System: {font_info['system']}")
    print(f"Current Font: {font_info['current_font']}")
    print(f"Available Emoji Fonts: {font_info['available_emoji_fonts']}")