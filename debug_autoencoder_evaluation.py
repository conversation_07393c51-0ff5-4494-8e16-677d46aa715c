"""
Debug script for autoencoder evaluation issues.
This script helps identify why the "Could not evaluate model performance" warning appears.
"""

import numpy as np
import pandas as pd
import torch
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

def debug_evaluation_process(val_df_scaled, val_truth_tensor, model, target_col, all_features, hparams, use_enhanced_preprocessing=True):
    """
    Debug the autoencoder evaluation process to identify failure points.
    
    Args:
        val_df_scaled: Scaled validation dataframe
        val_truth_tensor: Ground truth tensor
        model: Trained autoencoder model
        target_col: Target column name
        all_features: List of all feature names
        hparams: Hyperparameters dictionary
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
    """
    print("🔍 DEBUGGING AUTOENCODER EVALUATION PROCESS")
    print("=" * 60)
    
    # Import the create_sequences function
    try:
        from ml_core import create_sequences
        print("✅ Successfully imported create_sequences")
    except ImportError as e:
        print(f"❌ Failed to import create_sequences: {e}")
        return
    
    # Initialize metrics
    imputation_metrics = {'mae': -1, 'r2': -1, 'rmse': -1}
    prediction_metrics = {'mae': -1, 'r2': -1, 'rmse': -1}
    
    print(f"\n1. INITIAL STATE:")
    print(f"   • Validation data shape: {val_df_scaled.shape}")
    print(f"   • Ground truth tensor shape: {val_truth_tensor.shape}")
    print(f"   • Target column: {target_col}")
    print(f"   • Target index in features: {all_features.index(target_col)}")
    
    # Step 1: Test imputation evaluation (should work)
    print(f"\n2. TESTING IMPUTATION EVALUATION:")
    try:
        # Create validation sequences with some missing values
        val_train_sequences, _ = create_sequences(val_df_scaled, 'WELL', all_features,
                                                 sequence_len=hparams['sequence_len'],
                                                 use_enhanced=use_enhanced_preprocessing)
        
        if val_train_sequences.shape[0] > 0:
            val_train_tensor = torch.from_numpy(val_train_sequences.astype(np.float32))
            print(f"   ✅ Created validation sequences: {val_train_tensor.shape}")
            
            # Get model predictions
            if hasattr(model, 'predict_large_dataset'):
                imputed_val_tensor = model.predict_large_dataset(val_train_tensor)
            else:
                imputed_val_tensor = model.predict(val_train_tensor)
            
            print(f"   ✅ Got model predictions: {imputed_val_tensor.shape}")
            
            # Check for artificially missing values
            target_idx = all_features.index(target_col)
            val_mask = torch.isnan(val_train_tensor[:, :, target_idx])
            missing_count = val_mask.sum().item()
            
            print(f"   • Missing values in validation: {missing_count}")
            
            if val_mask.any():
                # Extract predictions and ground truth
                y_pred_val = imputed_val_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()
                y_true_val = val_truth_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()
                
                # Filter valid data
                valid_indices = ~np.isnan(y_true_val) & ~np.isnan(y_pred_val)
                y_pred_val = y_pred_val[valid_indices]
                y_true_val = y_true_val[valid_indices]
                
                print(f"   • Valid evaluation points: {len(y_true_val)}")
                
                if len(y_true_val) > 0:
                    imputation_metrics['mae'] = mean_absolute_error(y_true_val, y_pred_val)
                    imputation_metrics['r2'] = r2_score(y_true_val, y_pred_val)
                    imputation_metrics['rmse'] = np.sqrt(mean_squared_error(y_true_val, y_pred_val))
                    
                    print(f"   ✅ Imputation evaluation successful:")
                    print(f"      • MAE: {imputation_metrics['mae']:.4f}")
                    print(f"      • R²: {imputation_metrics['r2']:.4f}")
                    print(f"      • RMSE: {imputation_metrics['rmse']:.4f}")
                else:
                    print(f"   ❌ No valid evaluation points after filtering")
            else:
                print(f"   ❌ No artificially missing values found")
        else:
            print(f"   ❌ No validation sequences created")
            
    except Exception as e:
        print(f"   ❌ Imputation evaluation failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Step 2: Test prediction evaluation (likely to fail)
    print(f"\n3. TESTING PREDICTION EVALUATION:")
    try:
        # Create sequences with entirely masked target
        val_df_pred = val_df_scaled.copy()
        original_target_stats = val_df_pred[target_col].describe()
        print(f"   • Original target stats: mean={original_target_stats['mean']:.4f}, std={original_target_stats['std']:.4f}")
        
        val_df_pred[target_col] = np.nan  # Mask entire target
        print(f"   ✅ Masked entire target column")
        
        # Try to create sequences
        val_pred_sequences, _ = create_sequences(val_df_pred, 'WELL', all_features,
                                               sequence_len=hparams['sequence_len'],
                                               use_enhanced=use_enhanced_preprocessing)
        
        print(f"   • Prediction sequences shape: {val_pred_sequences.shape}")
        
        if val_pred_sequences.shape[0] > 0:
            val_pred_tensor = torch.from_numpy(val_pred_sequences.astype(np.float32))
            print(f"   ✅ Created prediction tensor: {val_pred_tensor.shape}")
            
            # Check tensor content
            target_idx = all_features.index(target_col)
            target_slice = val_pred_tensor[:, :, target_idx]
            nan_count = torch.isnan(target_slice).sum().item()
            total_count = target_slice.numel()
            
            print(f"   • Target slice NaN count: {nan_count}/{total_count} ({100*nan_count/total_count:.1f}%)")
            
            # Get predictions
            if hasattr(model, 'predict_large_dataset'):
                pred_output = model.predict_large_dataset(val_pred_tensor)
            else:
                pred_output = model.predict(val_pred_tensor)
            
            print(f"   ✅ Got prediction output: {pred_output.shape}")
            
            # Compare with ground truth
            y_pred_full = pred_output[:, :, target_idx].detach().cpu().numpy().flatten()
            y_true_full = val_truth_tensor[:, :, target_idx].detach().cpu().numpy().flatten()
            
            print(f"   • Flattened shapes: pred={y_pred_full.shape}, true={y_true_full.shape}")
            
            # Check for valid data
            valid_idx = ~np.isnan(y_true_full) & ~np.isnan(y_pred_full)
            valid_count = valid_idx.sum()
            
            print(f"   • Valid points for evaluation: {valid_count}")
            
            if valid_count > 0:
                y_pred_filtered = y_pred_full[valid_idx]
                y_true_filtered = y_true_full[valid_idx]
                
                prediction_metrics['mae'] = mean_absolute_error(y_true_filtered, y_pred_filtered)
                prediction_metrics['r2'] = r2_score(y_true_filtered, y_pred_filtered)
                prediction_metrics['rmse'] = np.sqrt(mean_squared_error(y_true_filtered, y_pred_filtered))
                
                print(f"   ✅ Prediction evaluation successful:")
                print(f"      • MAE: {prediction_metrics['mae']:.4f}")
                print(f"      • R²: {prediction_metrics['r2']:.4f}")
                print(f"      • RMSE: {prediction_metrics['rmse']:.4f}")
            else:
                print(f"   ❌ No valid points for prediction evaluation")
                print(f"      • All predictions NaN: {np.isnan(y_pred_full).all()}")
                print(f"      • All ground truth NaN: {np.isnan(y_true_full).all()}")
        else:
            print(f"   ❌ No prediction sequences created")
            
    except Exception as e:
        print(f"   ❌ Prediction evaluation failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Step 3: Analyze results
    print(f"\n4. FINAL ANALYSIS:")
    print(f"   • Imputation R²: {imputation_metrics['r2']}")
    print(f"   • Prediction R²: {prediction_metrics['r2']}")
    
    # Check the condition that triggers the warning
    condition_met = imputation_metrics['r2'] > 0 and prediction_metrics['r2'] > 0
    print(f"   • Evaluation condition met: {condition_met}")
    
    if not condition_met:
        print(f"   ❌ This will trigger the 'Could not evaluate model performance' warning")
        
        if imputation_metrics['r2'] <= 0:
            print(f"      • Imputation evaluation failed or returned invalid R²")
        if prediction_metrics['r2'] <= 0:
            print(f"      • Prediction evaluation failed or returned invalid R²")
    else:
        print(f"   ✅ Evaluation should succeed")
        
        # Calculate performance metrics
        r2_drop = imputation_metrics['r2'] - prediction_metrics['r2']
        performance_ratio = prediction_metrics['r2'] / imputation_metrics['r2'] if imputation_metrics['r2'] > 0 else 0
        
        print(f"   • R² Drop: {r2_drop:.4f}")
        print(f"   • Performance Ratio: {performance_ratio:.2%}")
    
    print("=" * 60)
    return imputation_metrics, prediction_metrics

if __name__ == "__main__":
    print("This is a debug utility. Import and call debug_evaluation_process() with your data.")
