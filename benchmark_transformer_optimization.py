#!/usr/bin/env python3
"""
Transformer Model Optimization Benchmark Script

This script compares the performance of original vs optimized transformer configurations
to measure speed improvements and accuracy trade-offs.
"""

import time
import torch
import numpy as np
from typing import Dict, Any
import matplotlib.pyplot as plt
import pandas as pd

def generate_test_data(n_samples: int = 1000, n_features: int = 5, sequence_len: int = 64):
    """Generate synthetic time series data for testing."""
    np.random.seed(42)
    
    # Generate synthetic well log data with realistic patterns
    data = np.random.randn(n_samples, sequence_len, n_features)
    
    # Add some realistic patterns
    for i in range(n_features):
        # Add trend
        trend = np.linspace(0, 2, sequence_len)
        data[:, :, i] += trend
        
        # Add some periodic patterns
        t = np.linspace(0, 4*np.pi, sequence_len)
        data[:, :, i] += 0.5 * np.sin(t + i)
    
    # Create missing values (20% missing)
    mask = np.random.random((n_samples, sequence_len, n_features)) < 0.2
    data_with_missing = data.copy()
    data_with_missing[mask] = np.nan
    
    return torch.tensor(data_with_missing, dtype=torch.float32), torch.tensor(data, dtype=torch.float32)

def benchmark_configuration(config_name: str, params: Dict[str, Any], 
                          train_data: torch.Tensor, truth_data: torch.Tensor,
                          test_data: torch.Tensor, test_truth: torch.Tensor) -> Dict[str, Any]:
    """Benchmark a specific transformer configuration."""
    
    print(f"\n🧪 Testing {config_name} configuration...")
    print(f"   Parameters: {params}")
    
    try:
        # Import here to avoid circular imports
        from models.advanced_models.transformer_model import TransformerModel
        
        # Initialize model with test parameters
        model = TransformerModel(
            n_features=train_data.shape[2],
            sequence_len=train_data.shape[1],
            epochs=5,  # Reduced for benchmarking
            **params
        )
        
        # Measure parameter count and memory
        param_count = model._estimate_parameters()
        memory_mb = model._estimate_memory_mb()
        
        print(f"   Model parameters: {param_count:,}")
        print(f"   Estimated memory: {memory_mb:.1f} MB")
        
        # Measure training time
        print("   🏃 Training...")
        torch.cuda.reset_peak_memory_stats() if torch.cuda.is_available() else None
        
        start_time = time.time()
        model.fit(train_data, truth_data)
        training_time = time.time() - start_time
        
        # Measure peak memory during training
        peak_memory_mb = 0
        if torch.cuda.is_available():
            peak_memory_mb = torch.cuda.max_memory_allocated() / (1024**2)
        
        # Measure inference time
        print("   🔮 Inference...")
        start_time = time.time()
        predictions = model.predict(test_data)
        inference_time = time.time() - start_time
        
        # Calculate accuracy metrics
        # Handle NaN values in predictions
        valid_mask = ~torch.isnan(predictions) & ~torch.isnan(test_truth)
        if valid_mask.sum() > 0:
            pred_valid = predictions[valid_mask].cpu().numpy()
            truth_valid = test_truth[valid_mask].cpu().numpy()
            
            mse = np.mean((pred_valid - truth_valid) ** 2)
            mae = np.mean(np.abs(pred_valid - truth_valid))
            
            # R² score
            ss_res = np.sum((truth_valid - pred_valid) ** 2)
            ss_tot = np.sum((truth_valid - np.mean(truth_valid)) ** 2)
            r2_score = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        else:
            mse, mae, r2_score = float('inf'), float('inf'), 0
        
        results = {
            'config_name': config_name,
            'parameters': param_count,
            'estimated_memory_mb': memory_mb,
            'peak_memory_mb': peak_memory_mb,
            'training_time': training_time,
            'inference_time': inference_time,
            'time_per_epoch': training_time / 5,
            'samples_per_second': len(test_data) / inference_time,
            'mse': mse,
            'mae': mae,
            'r2_score': r2_score,
            'config_params': params
        }
        
        print(f"   ✅ Training time: {training_time:.2f}s ({training_time/5:.2f}s per epoch)")
        print(f"   ✅ Inference time: {inference_time:.2f}s ({len(test_data)/inference_time:.1f} samples/s)")
        print(f"   ✅ Peak memory: {peak_memory_mb:.1f} MB")
        print(f"   ✅ MSE: {mse:.6f}, R²: {r2_score:.4f}")
        
        return results
        
    except Exception as e:
        print(f"   ❌ Error testing {config_name}: {e}")
        return {
            'config_name': config_name,
            'error': str(e),
            'parameters': 0,
            'training_time': float('inf'),
            'inference_time': float('inf')
        }

def run_optimization_benchmark():
    """Run comprehensive transformer optimization benchmark."""
    
    print("🚀 Transformer Model Optimization Benchmark")
    print("=" * 60)
    
    # Generate test data
    print("📊 Generating test data...")
    train_data, train_truth = generate_test_data(n_samples=800, n_features=5, sequence_len=64)
    test_data, test_truth = generate_test_data(n_samples=200, n_features=5, sequence_len=64)
    
    print(f"   Train data shape: {train_data.shape}")
    print(f"   Test data shape: {test_data.shape}")
    
    # Define test configurations
    configs = {
        'original': {
            'd_model': 256, 
            'n_heads': 8, 
            'n_encoder_layers': 6, 
            'd_ff': 1024, 
            'batch_size': 32
        },
        'balanced_optimized': {
            'd_model': 192, 
            'n_heads': 6, 
            'n_encoder_layers': 4, 
            'd_ff': 768, 
            'batch_size': 48
        },
        'aggressive_optimized': {
            'd_model': 128, 
            'n_heads': 4, 
            'n_encoder_layers': 3, 
            'd_ff': 512, 
            'batch_size': 64
        },
        'conservative_optimized': {
            'd_model': 256, 
            'n_heads': 8, 
            'n_encoder_layers': 4, 
            'd_ff': 512, 
            'batch_size': 40
        }
    }
    
    # Run benchmarks
    results = []
    for config_name, params in configs.items():
        result = benchmark_configuration(
            config_name, params, train_data, train_truth, test_data, test_truth
        )
        results.append(result)
    
    # Analyze results
    print("\n📈 Benchmark Results Summary")
    print("=" * 60)
    
    # Create results DataFrame
    df = pd.DataFrame(results)
    
    # Calculate improvements relative to original
    if len(df) > 0 and 'original' in df['config_name'].values:
        original_idx = df[df['config_name'] == 'original'].index[0]
        original_training_time = df.loc[original_idx, 'training_time']
        original_memory = df.loc[original_idx, 'peak_memory_mb']
        original_r2 = df.loc[original_idx, 'r2_score']
        
        for idx, row in df.iterrows():
            if row['config_name'] != 'original':
                speed_improvement = (original_training_time - row['training_time']) / original_training_time * 100
                memory_reduction = (original_memory - row['peak_memory_mb']) / original_memory * 100
                accuracy_change = (row['r2_score'] - original_r2) / original_r2 * 100
                
                print(f"\n{row['config_name'].upper()}:")
                print(f"   Speed improvement: {speed_improvement:.1f}%")
                print(f"   Memory reduction: {memory_reduction:.1f}%")
                print(f"   Accuracy change: {accuracy_change:+.1f}%")
                print(f"   Parameters: {row['parameters']:,} ({(row['parameters']/df.loc[original_idx, 'parameters']-1)*100:+.1f}%)")
    
    # Save detailed results
    df.to_csv('transformer_optimization_results.csv', index=False)
    print(f"\n💾 Detailed results saved to 'transformer_optimization_results.csv'")
    
    # Create visualization
    create_benchmark_plots(df)
    
    return df

def create_benchmark_plots(df: pd.DataFrame):
    """Create visualization plots for benchmark results."""
    
    if len(df) == 0:
        return
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Transformer Optimization Benchmark Results', fontsize=16)
    
    # Training time comparison
    axes[0, 0].bar(df['config_name'], df['training_time'])
    axes[0, 0].set_title('Training Time (5 epochs)')
    axes[0, 0].set_ylabel('Time (seconds)')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Memory usage comparison
    axes[0, 1].bar(df['config_name'], df['peak_memory_mb'])
    axes[0, 1].set_title('Peak Memory Usage')
    axes[0, 1].set_ylabel('Memory (MB)')
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # Parameter count comparison
    axes[1, 0].bar(df['config_name'], df['parameters'])
    axes[1, 0].set_title('Model Parameters')
    axes[1, 0].set_ylabel('Parameter Count')
    axes[1, 0].tick_params(axis='x', rotation=45)
    
    # Accuracy comparison
    axes[1, 1].bar(df['config_name'], df['r2_score'])
    axes[1, 1].set_title('Model Accuracy (R² Score)')
    axes[1, 1].set_ylabel('R² Score')
    axes[1, 1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('transformer_optimization_benchmark.png', dpi=300, bbox_inches='tight')
    print(f"📊 Benchmark plots saved to 'transformer_optimization_benchmark.png'")

if __name__ == "__main__":
    # Run the benchmark
    results_df = run_optimization_benchmark()
    
    print("\n🎯 Benchmark completed!")
    print("   Check 'transformer_optimization_results.csv' for detailed results")
    print("   Check 'transformer_optimization_benchmark.png' for visualizations")
