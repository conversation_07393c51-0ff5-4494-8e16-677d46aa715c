"""
Data Leakage Detection Module for ML Log Prediction

This module provides functions to detect and prevent data leakage in machine learning
pipelines for well log prediction. It includes checks for temporal leakage, perfect
correlations, and other common sources of data leakage.
"""

import numpy as np
import pandas as pd
from sklearn.metrics import r2_score
from typing import Dict, List, Tuple, Optional, Any
import warnings


def detect_perfect_correlation_leakage(train_df: pd.DataFrame, val_df: pd.DataFrame, 
                                     test_df: pd.DataFrame, feature_cols: List[str], 
                                     target_col: str, threshold: float = 0.95) -> Dict[str, Any]:
    """
    Detect potential data leakage by checking for suspiciously high correlations
    between features and target across different splits.
    
    Args:
        train_df: Training dataframe
        val_df: Validation dataframe  
        test_df: Test dataframe
        feature_cols: List of feature column names
        target_col: Target column name
        threshold: Correlation threshold above which to flag potential leakage
        
    Returns:
        Dictionary with leakage detection results
    """
    print("🔍 Detecting perfect correlation leakage...")
    
    results = {
        'leakage_detected': False,
        'high_correlations': [],
        'suspicious_features': [],
        'recommendations': []
    }
    
    # Check correlations in each split
    splits = {'train': train_df, 'validation': val_df, 'test': test_df}
    
    for split_name, df in splits.items():
        if df.empty:
            continue
            
        # Calculate correlations between features and target
        correlations = {}
        for feature in feature_cols:
            if feature in df.columns and target_col in df.columns:
                # Only calculate correlation on non-missing values
                valid_mask = df[feature].notna() & df[target_col].notna()
                if valid_mask.sum() > 10:  # Need at least 10 valid points
                    corr = df.loc[valid_mask, feature].corr(df.loc[valid_mask, target_col])
                    correlations[feature] = abs(corr) if not np.isnan(corr) else 0
        
        # Check for suspiciously high correlations
        high_corr_features = []
        for feature, corr in correlations.items():
            if corr > threshold:
                high_corr_features.append((feature, corr))
                results['leakage_detected'] = True
        
        if high_corr_features:
            results['high_correlations'].append({
                'split': split_name,
                'features': high_corr_features
            })
            
            for feature, corr in high_corr_features:
                if feature not in results['suspicious_features']:
                    results['suspicious_features'].append(feature)
    
    # Generate recommendations
    if results['leakage_detected']:
        results['recommendations'].extend([
            f"⚠️ Suspiciously high correlations detected (>{threshold:.2f})",
            "• Check if target information is leaking into features",
            "• Verify temporal ordering of data splits",
            "• Review feature engineering process",
            "• Consider removing highly correlated features"
        ])
        
        print("🚨 POTENTIAL DATA LEAKAGE DETECTED!")
        for item in results['high_correlations']:
            print(f"   {item['split'].upper()} split:")
            for feature, corr in item['features']:
                print(f"     - {feature}: correlation = {corr:.3f}")
    else:
        results['recommendations'].append("✅ No perfect correlation leakage detected")
        print("✅ No perfect correlation leakage detected")
    
    return results


def validate_temporal_split(train_df: pd.DataFrame, val_df: pd.DataFrame, 
                           test_df: pd.DataFrame, well_col: str = 'WELL', 
                           depth_col: str = 'MD') -> Dict[str, Any]:
    """
    Validate that temporal splits are properly implemented without future information
    leaking into past predictions.
    
    Args:
        train_df: Training dataframe
        val_df: Validation dataframe
        test_df: Test dataframe
        well_col: Well identifier column name
        depth_col: Depth/time column name
        
    Returns:
        Dictionary with temporal validation results
    """
    print("🕐 Validating temporal split integrity...")
    
    results = {
        'temporal_leakage_detected': False,
        'violations': [],
        'split_summary': {},
        'recommendations': []
    }
    
    # Check each well for temporal ordering violations
    all_wells = set()
    if not train_df.empty:
        all_wells.update(train_df[well_col].unique())
    if not val_df.empty:
        all_wells.update(val_df[well_col].unique())
    if not test_df.empty:
        all_wells.update(test_df[well_col].unique())
    
    violations = []
    
    for well in all_wells:
        well_violations = []
        
        # Get depth ranges for each split
        train_depths = []
        val_depths = []
        test_depths = []
        
        if not train_df.empty and well in train_df[well_col].values:
            train_well = train_df[train_df[well_col] == well]
            train_depths = [train_well[depth_col].min(), train_well[depth_col].max()]
        
        if not val_df.empty and well in val_df[well_col].values:
            val_well = val_df[val_df[well_col] == well]
            val_depths = [val_well[depth_col].min(), val_well[depth_col].max()]
            
        if not test_df.empty and well in test_df[well_col].values:
            test_well = test_df[test_df[well_col] == well]
            test_depths = [test_well[depth_col].min(), test_well[depth_col].max()]
        
        # Check for temporal violations
        # Training should generally be shallower than validation
        if train_depths and val_depths:
            if train_depths[1] > val_depths[0]:  # Training max > validation min
                well_violations.append(f"Training data extends deeper than validation start")
        
        # Validation should generally be shallower than test (if same well)
        if val_depths and test_depths:
            if val_depths[1] > test_depths[0]:  # Validation max > test min
                well_violations.append(f"Validation data extends deeper than test start")
        
        # Training should not overlap with test
        if train_depths and test_depths:
            if not (train_depths[1] < test_depths[0] or test_depths[1] < train_depths[0]):
                well_violations.append(f"Training and test data overlap in depth")
        
        if well_violations:
            violations.append({
                'well': well,
                'violations': well_violations,
                'train_depth_range': train_depths,
                'val_depth_range': val_depths,
                'test_depth_range': test_depths
            })
    
    results['violations'] = violations
    results['temporal_leakage_detected'] = len(violations) > 0
    
    # Generate split summary
    results['split_summary'] = {
        'total_wells': len(all_wells),
        'wells_with_violations': len(violations),
        'train_wells': len(train_df[well_col].unique()) if not train_df.empty else 0,
        'val_wells': len(val_df[well_col].unique()) if not val_df.empty else 0,
        'test_wells': len(test_df[well_col].unique()) if not test_df.empty else 0
    }
    
    # Generate recommendations
    if results['temporal_leakage_detected']:
        results['recommendations'].extend([
            "🚨 Temporal leakage detected in data splits!",
            "• Ensure training data comes from shallower depths than validation/test",
            "• Verify that future information is not used to predict past events",
            "• Consider using strict temporal ordering in splits",
            "• Review well-by-well depth progression"
        ])
        
        print("🚨 TEMPORAL LEAKAGE DETECTED!")
        for violation in violations:
            print(f"   Well {violation['well']}:")
            for v in violation['violations']:
                print(f"     - {v}")
    else:
        results['recommendations'].append("✅ No temporal leakage detected in splits")
        print("✅ No temporal leakage detected in splits")
    
    return results


def detect_target_leakage_in_features(df: pd.DataFrame, feature_cols: List[str], 
                                     target_col: str) -> Dict[str, Any]:
    """
    Detect if target information has leaked into feature columns.
    
    Args:
        df: Input dataframe
        feature_cols: List of feature column names
        target_col: Target column name
        
    Returns:
        Dictionary with target leakage detection results
    """
    print("🎯 Detecting target leakage in features...")
    
    results = {
        'target_leakage_detected': False,
        'suspicious_features': [],
        'perfect_matches': [],
        'recommendations': []
    }
    
    if target_col not in df.columns:
        results['recommendations'].append("⚠️ Target column not found in dataframe")
        return results
    
    # Check each feature for perfect or near-perfect correlation with target
    for feature in feature_cols:
        if feature not in df.columns:
            continue
            
        # Get valid (non-missing) pairs
        valid_mask = df[feature].notna() & df[target_col].notna()
        
        if valid_mask.sum() < 10:  # Need at least 10 valid points
            continue
            
        feature_vals = df.loc[valid_mask, feature]
        target_vals = df.loc[valid_mask, target_col]
        
        # Check for perfect matches (identical values)
        if np.allclose(feature_vals, target_vals, rtol=1e-10):
            results['perfect_matches'].append(feature)
            results['target_leakage_detected'] = True
            
        # Check for suspiciously high correlation
        correlation = feature_vals.corr(target_vals)
        if abs(correlation) > 0.99:
            results['suspicious_features'].append((feature, correlation))
            results['target_leakage_detected'] = True
    
    # Generate recommendations
    if results['target_leakage_detected']:
        results['recommendations'].extend([
            "🚨 Target leakage detected in features!",
            "• Remove features that are identical to target",
            "• Check feature engineering pipeline for target contamination",
            "• Verify that future target values are not used as features",
            "• Review data preprocessing steps"
        ])
        
        print("🚨 TARGET LEAKAGE DETECTED!")
        if results['perfect_matches']:
            print(f"   Perfect matches: {results['perfect_matches']}")
        if results['suspicious_features']:
            print("   Suspicious correlations:")
            for feature, corr in results['suspicious_features']:
                print(f"     - {feature}: {corr:.4f}")
    else:
        results['recommendations'].append("✅ No target leakage detected in features")
        print("✅ No target leakage detected in features")
    
    return results


def comprehensive_leakage_check(train_df: pd.DataFrame, val_df: pd.DataFrame, 
                               test_df: pd.DataFrame, feature_cols: List[str], 
                               target_col: str, well_col: str = 'WELL', 
                               depth_col: str = 'MD') -> Dict[str, Any]:
    """
    Perform a comprehensive check for all types of data leakage.
    
    Args:
        train_df: Training dataframe
        val_df: Validation dataframe
        test_df: Test dataframe
        feature_cols: List of feature column names
        target_col: Target column name
        well_col: Well identifier column name
        depth_col: Depth/time column name
        
    Returns:
        Dictionary with comprehensive leakage detection results
    """
    print("🔍 Performing comprehensive data leakage check...")
    print("=" * 60)
    
    # Run all leakage detection checks
    correlation_results = detect_perfect_correlation_leakage(
        train_df, val_df, test_df, feature_cols, target_col
    )
    
    temporal_results = validate_temporal_split(
        train_df, val_df, test_df, well_col, depth_col
    )
    
    # Check target leakage in combined dataset
    combined_df = pd.concat([train_df, val_df, test_df], ignore_index=True)
    target_results = detect_target_leakage_in_features(
        combined_df, feature_cols, target_col
    )
    
    # Combine results
    comprehensive_results = {
        'overall_leakage_detected': (
            correlation_results['leakage_detected'] or 
            temporal_results['temporal_leakage_detected'] or 
            target_results['target_leakage_detected']
        ),
        'correlation_leakage': correlation_results,
        'temporal_leakage': temporal_results,
        'target_leakage': target_results,
        'summary': {},
        'all_recommendations': []
    }
    
    # Generate summary
    checks_failed = sum([
        correlation_results['leakage_detected'],
        temporal_results['temporal_leakage_detected'],
        target_results['target_leakage_detected']
    ])

    comprehensive_results['summary'] = {
        'total_checks_performed': 3,
        'checks_failed': checks_failed,
        'data_quality_score': 1.0 - (checks_failed / 3)
    }
    
    # Collect all recommendations
    all_recommendations = []
    all_recommendations.extend(correlation_results['recommendations'])
    all_recommendations.extend(temporal_results['recommendations'])
    all_recommendations.extend(target_results['recommendations'])
    comprehensive_results['all_recommendations'] = all_recommendations
    
    # Print summary
    print("=" * 60)
    print("📊 COMPREHENSIVE LEAKAGE CHECK SUMMARY")
    print("=" * 60)
    
    if comprehensive_results['overall_leakage_detected']:
        print("🚨 DATA LEAKAGE DETECTED!")
        print(f"   Failed checks: {comprehensive_results['summary']['checks_failed']}/3")
        print(f"   Data quality score: {comprehensive_results['summary']['data_quality_score']:.2f}")
        print("\n📋 All Recommendations:")
        for rec in all_recommendations:
            print(f"   {rec}")
    else:
        print("✅ NO DATA LEAKAGE DETECTED!")
        print("   All checks passed successfully")
        print(f"   Data quality score: {comprehensive_results['summary']['data_quality_score']:.2f}")
    
    print("=" * 60)
    
    return comprehensive_results
