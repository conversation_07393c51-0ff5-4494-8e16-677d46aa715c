#!/usr/bin/env python3
"""
Test script to run the TEFN forecasting example with the fixes
"""

import sys
import os

# Add the example directory to the path
sys.path.append('example')

def test_tefn_forecasting():
    """Test the TEFN forecasting with the visualization fixes."""
    print("🚀 Testing TEFN Forecasting with Visualization Fixes")
    print("="*80)
    
    try:
        # Import the fixed TEFN function
        from Pypots_quick_start_tutor import run_tefn_forecasting
        
        print("📦 Successfully imported TEFN forecasting function")
        print("🎯 Starting TEFN forecasting example...")
        print("\nNote: This will:")
        print("  1. Load and prepare the PhysioNet-2012 dataset")
        print("  2. Train the TEFN model (may take several minutes)")
        print("  3. Run inference and calculate MAE")
        print("  4. Generate both standard and enhanced visualizations")
        print("  5. Save plots to the 'plots' directory")
        
        # Run the TEFN forecasting example
        run_tefn_forecasting()
        
        print("\n" + "="*80)
        print("✅ TEFN Forecasting Test Completed Successfully!")
        print("="*80)
        print("📊 Check the 'plots' directory for generated visualizations:")
        print("  • tefn_forecasting_results.png - Standard 4-panel analysis")
        print("  • tefn_enhanced_forecasting_results.png - Advanced 6-panel analysis")
        print("  • Any error diagnostic plots (if needed)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {str(e)}")
        print("   Make sure you're in the correct directory and have all dependencies installed.")
        return False
        
    except Exception as e:
        print(f"❌ TEFN Forecasting Error: {str(e)}")
        print("   The error occurred during model training or visualization.")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """Check if the environment is properly set up."""
    print("🔍 Checking Environment Setup")
    print("="*50)
    
    # Check if we're in the right directory
    if os.path.exists('example/Pypots_quick_start_tutor.py'):
        print("✅ Found example/Pypots_quick_start_tutor.py")
    else:
        print("❌ Cannot find example/Pypots_quick_start_tutor.py")
        print("   Make sure you're running this from the project root directory")
        return False
    
    # Check for required packages
    required_packages = ['pypots', 'numpy', 'matplotlib', 'benchpots']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is available")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("   Install them using: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ Environment setup looks good!")
    return True

def main():
    """Main test function."""
    print("🧪 TEFN Visualization Fix Test")
    print("="*80)
    
    # Check environment first
    if not check_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        return
    
    print("\n" + "="*80)
    print("🎯 Running TEFN Forecasting Test")
    print("="*80)
    print("⏰ This test will take several minutes to complete...")
    print("   The model needs to train for 10 epochs on the PhysioNet dataset.")
    print("   Please be patient while the test runs.")
    
    # Ask for confirmation
    try:
        response = input("\n🤔 Do you want to proceed with the full TEFN test? (y/n): ").lower().strip()
        if response not in ['y', 'yes']:
            print("❌ Test cancelled by user.")
            return
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user.")
        return
    
    # Run the test
    success = test_tefn_forecasting()
    
    # Final summary
    print("\n" + "="*80)
    print("📋 Test Summary")
    print("="*80)
    
    if success:
        print("🎉 TEFN visualization fix test PASSED!")
        print("✅ The dimension mismatch error has been resolved.")
        print("✅ Both standard and enhanced visualizations should be generated.")
        print("✅ Model training and inference completed successfully.")
        print("\n📁 Next steps:")
        print("   1. Check the 'plots' directory for generated visualizations")
        print("   2. Review the visualization quality and insights")
        print("   3. The fix is ready for production use!")
    else:
        print("❌ TEFN visualization fix test FAILED!")
        print("   Please review the error messages above and:")
        print("   1. Check that all dependencies are installed")
        print("   2. Verify you're in the correct directory")
        print("   3. Check the error logs for specific issues")

if __name__ == "__main__":
    main()
