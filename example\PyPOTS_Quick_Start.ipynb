{
  "cells": [
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "DonG1Q5K8lFi"
      },
      "source": [
        "# <a href=\"https://github.com/WenjieDu/PyPOTS\"><img src=\"https://pypots.com/figs/pypots_logos/PyPOTS/logo_F5BG.svg\" alt=\"\" aria-label=\"logo\" height=\"60\" align='center'></a>Quick-start Tutorial for [PyPOTS](https://github.com/WenjieDu/PyPOTS) is Here!"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "SIdbBlXnftMy"
      },
      "source": [
        "#### 🤗 Hello there, in this quick-start tutorial, we show you how to run deep learning models in PyPOTS that is really simple ;~) PyPOTS supports five common anslysis tasks (imputation, forecasting, classification, clustering, and anomaly detection) on time series even your data contains missing values. For each analysis task, we select two algorithoms from PyPOTS. Surely you can try other models in PyPOTS (refer to [the model table here](https://github.com/WenjieDu/PyPOTS/?tab=readme-ov-file#-available-algorithms) where **50+** algorithms listed).\n",
        "\n",
        "#### Enough for introduction. Let's get hands on it!"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "nDZqwFN4899V"
      },
      "source": [
        "## Dependency Installation"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "xe-rPZzC9CoW"
      },
      "outputs": [],
      "source": [
        "# Install required packages first:
# pip install pypots==v0.18
# pip install benchpots

# Or use subprocess if running as Python script:
# import subprocess
# subprocess.run(["pip", "install", "pypots==v0.18"])
# subprocess.run(["pip", "install", "benchpots"])"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "M4QHQOPd0dXW"
      },
      "outputs": [],
      "source": [
        "import pypots"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "StrWh_It88lF"
      },
      "source": [
        "## 📀 Preparing the **PhysioNet-2012** dataset for this tutorial"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/",
          "height": 610
        },
        "id": "yASJSepF17za",
        "outputId": "f4a74e08-9092-46c8-9a1c-fb9ab3a83350"
      },
      "outputs": [
        {
          "ename": "ModuleNotFoundError",
          "evalue": "No module named 'benchpots'",
          "output_type": "error",
          "traceback": [
            "\u001b[0;31m---------------------------------------------------------------------------\u001b[0m",
            "\u001b[0;31mModuleNotFoundError\u001b[0m                       Traceback (most recent call last)",
            "\u001b[0;32m/tmp/ipython-input-1-3150747962.py\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mnumpy\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0;32mimport\u001b[0m \u001b[0mbenchpots\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      3\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0mpypots\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mutils\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mrandom\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mset_random_seed\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0mset_random_seed\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n",
            "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'benchpots'",
            "",
            "\u001b[0;31m---------------------------------------------------------------------------\u001b[0;32m\nNOTE: If your import is failing due to a missing package, you can\nmanually install dependencies using either !pip or !apt.\n\nTo view examples of installing some common dependencies, click the\n\"Open Examples\" button below.\n\u001b[0;31m---------------------------------------------------------------------------\u001b[0m\n"
          ]
        }
      ],
      "source": [
        "import numpy as np\n",
        "import benchpots\n",
        "from pypots.utils.random import set_random_seed\n",
        "\n",
        "set_random_seed()\n",
        "\n",
        "# Load the PhysioNet-2012 dataset\n",
        "physionet2012_dataset = benchpots.datasets.preprocess_physionet2012(\n",
        "    subset=\"set-a\",\n",
        "    rate=0.1,  # the rate of missing values artificially created to evaluate algorithms\n",
        ")\n",
        "\n",
        "# Take a look at the generated PhysioNet-2012 dataset, you'll find that everything has been prepared for you,\n",
        "# data splitting, normalization, additional artificially-missing values for evaluation, etc.\n",
        "print(physionet2012_dataset.keys())"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "lc3SoTqg9r2M"
      },
      "source": [
        "## 🌟 Imputation Models"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "YQMxsAjtnUyQ"
      },
      "source": [
        "### 💿 Assembel the dateset for the imputation task"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "Z11KSsFu-A9q"
      },
      "outputs": [],
      "source": [
        "# assemble the datasets for training\n",
        "dataset_for_IMPU_training = {\n",
        "    \"X\": physionet2012_dataset['train_X'],\n",
        "}\n",
        "# assemble the datasets for validation\n",
        "dataset_for_IMPU_validating = {\n",
        "    \"X\": physionet2012_dataset['val_X'],\n",
        "    \"X_ori\": physionet2012_dataset['val_X_ori'],\n",
        "}\n",
        "# assemble the datasets for test\n",
        "dataset_for_IMPU_testing = {\n",
        "    \"X\": physionet2012_dataset['test_X'],\n",
        "}\n",
        "## calculate the mask to indicate the ground truth positions in test_X_ori, will be used by metric funcs to evaluate models\n",
        "test_X_indicating_mask = np.isnan(physionet2012_dataset['test_X_ori']) ^ np.isnan(physionet2012_dataset['test_X'])\n",
        "test_X_ori = np.nan_to_num(physionet2012_dataset['test_X_ori'])  # metric functions do not accpet input with NaNs, hence fill NaNs with 0"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "oVqyqt_S9z3E"
      },
      "source": [
        "### 🚀 An example of [**SAITS**](https://arxiv.org/abs/2202.08516) for imputation"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "kcxUvLNE9rNL"
      },
      "outputs": [],
      "source": [
        "from pypots.nn.functional import calc_mae\n",
        "from pypots.optim import Adam\n",
        "from pypots.imputation import SAITS\n",
        "\n",
        "# initialize the model\n",
        "saits = SAITS(\n",
        "    n_steps=physionet2012_dataset['n_steps'],\n",
        "    n_features=physionet2012_dataset['n_features'],\n",
        "    n_layers=1,\n",
        "    d_model=256,\n",
        "    d_ffn=128,\n",
        "    n_heads=4,\n",
        "    d_k=64,\n",
        "    d_v=64,\n",
        "    dropout=0.1,\n",
        "    ORT_weight=1,  # you can adjust the weight values of arguments ORT_weight\n",
        "    # and MIT_weight to make the SAITS model focus more on one task. Usually you can just leave them to the default values, i.e. 1.\n",
        "    MIT_weight=1,\n",
        "    batch_size=32,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/imputation/saits\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "saits.fit(\n",
        "    train_set = dataset_for_IMPU_training,\n",
        "    val_set=dataset_for_IMPU_validating\n",
        ")\n",
        "\n",
        "# the testing stage, impute the originally-missing values and artificially-missing values in the test set\n",
        "saits_results = saits.predict(dataset_for_IMPU_testing)\n",
        "saits_imputation = saits_results[\"imputation\"]\n",
        "\n",
        "# calculate mean absolute error on the ground truth (artificially-missing values)\n",
        "testing_mae = calc_mae(\n",
        "    saits_imputation,\n",
        "    test_X_ori,\n",
        "    test_X_indicating_mask,\n",
        ")\n",
        "print(f\"Testing mean absolute error: {testing_mae:.4f}\")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "YDD1__JXOffB"
      },
      "source": [
        "### 🚀 An example of [**CSDI**](https://arxiv.org/abs/2107.03502) for imputation"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "C9HnXB8QO0Zp"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.imputation import CSDI\n",
        "from pypots.nn.functional import calc_mae\n",
        "\n",
        "# initialize the model\n",
        "csdi = CSDI(\n",
        "    n_steps=physionet2012_dataset['n_steps'],\n",
        "    n_features=physionet2012_dataset['n_features'],\n",
        "    n_layers=3,\n",
        "    n_heads=2,\n",
        "    n_channels=128,\n",
        "    d_time_embedding=64,\n",
        "    d_feature_embedding=32,\n",
        "    d_diffusion_embedding=128,\n",
        "    target_strategy=\"random\",\n",
        "    n_diffusion_steps=50,\n",
        "    batch_size=32,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/imputation/csdi\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "csdi.fit(\n",
        "    train_set = dataset_for_IMPU_training,\n",
        "    val_set = dataset_for_IMPU_validating,\n",
        ")\n",
        "\n",
        "# the testing stage, impute the originally-missing values and artificially-missing values in the test set\n",
        "csdi_results = csdi.predict(\n",
        "    dataset_for_IMPU_testing,\n",
        "    n_sampling_times = 2,  # CSDI has an argument to control the number of sampling times during inference\n",
        ")\n",
        "csdi_imputation = csdi_results[\"imputation\"]\n",
        "\n",
        "print(f\"The shape of csdi_imputation is {csdi_imputation.shape}\")\n",
        "\n",
        "# for error calculation, we need to take the mean value of the multiple samplings for each data sample\n",
        "mean_csdi_imputation = csdi_imputation.mean(axis=1)\n",
        "\n",
        "# calculate mean absolute error on the ground truth (artificially-missing values)\n",
        "testing_mae = calc_mae(\n",
        "    mean_csdi_imputation,\n",
        "    test_X_ori,\n",
        "    test_X_indicating_mask,\n",
        ")\n",
        "print(f\"Testing mean absolute error: {testing_mae:.4f}\")\n"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "UoR-KkgtAiZx"
      },
      "source": [
        "## 🌟 Forecasting Models"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "bfFinGSRsYcc"
      },
      "source": [
        "### 💿 Assemble the dateset for the forecasting task"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "dvsTR8ARAh5P"
      },
      "outputs": [],
      "source": [
        "# Assemble the datasets for training, validating, and testing.\n",
        "\n",
        "N_PRED_STEPS = 6\n",
        "\n",
        "dataset_for_FORE_training = {\n",
        "    \"X\": physionet2012_dataset['train_X'][:, :-N_PRED_STEPS],\n",
        "    \"X_pred\": physionet2012_dataset['train_X'][:, -N_PRED_STEPS:],\n",
        "}\n",
        "\n",
        "dataset_for_FORE_validating = {\n",
        "    \"X\": physionet2012_dataset['val_X'][:, :-N_PRED_STEPS],\n",
        "    \"X_pred\": physionet2012_dataset['val_X_ori'][:, -N_PRED_STEPS:],\n",
        "}\n",
        "\n",
        "dataset_for_FORE_testing = {\n",
        "    \"X\": physionet2012_dataset['test_X'][:, :-N_PRED_STEPS],  # we only take the first 42 steps for model input,\n",
        "    # and let the model forecast the left 6 steps\n",
        "}"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "jVIB7HhdAn2V"
      },
      "source": [
        "### 🚀 An example of [**TEFN**](https://arxiv.org/abs/2405.06419) for forecasting"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "brxb6kAoAq6g"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.forecasting import TEFN\n",
        "from pypots.nn.functional import calc_mae\n",
        "\n",
        "# initialize the model\n",
        "tefn = TEFN(\n",
        "    n_steps = physionet2012_dataset[\"n_steps\"] - N_PRED_STEPS,\n",
        "    n_features = physionet2012_dataset[\"n_features\"],\n",
        "    n_pred_steps = N_PRED_STEPS,\n",
        "    n_pred_features = physionet2012_dataset[\"n_features\"],\n",
        "    n_fod = 2,\n",
        "    batch_size=32,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/forecasting/tefn\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "tefn.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)\n",
        "# BTTF does not need to run func fits().\n",
        "\n",
        "# the testing stage\n",
        "tefn_results = tefn.predict(dataset_for_FORE_testing)\n",
        "tefn_prediction = tefn_results[\"forecasting\"]\n",
        "\n",
        "# calculate the mean absolute error on the ground truth in the forecasting task\n",
        "testing_mae = calc_mae(\n",
        "    tefn_prediction,\n",
        "    np.nan_to_num(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:]),\n",
        "    (~np.isnan(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:])).astype(int),\n",
        ")\n",
        "print(f\"Testing mean absolute error: {testing_mae:.4f}\")\n"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "v32m7J5V3Uwt"
      },
      "source": [
        "### 🚀 An example of [**TimeMixer++**](https://arxiv.org/abs/2410.16032) for forecasting"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "A28kXL2P3IfS"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.forecasting import TimeMixer\n",
        "from pypots.nn.functional import calc_mae\n",
        "\n",
        "timemixer = TimeMixer(\n",
        "    n_steps = physionet2012_dataset[\"n_steps\"] - N_PRED_STEPS,\n",
        "    n_features = physionet2012_dataset[\"n_features\"],\n",
        "    n_pred_steps = N_PRED_STEPS,\n",
        "    n_pred_features = physionet2012_dataset[\"n_features\"],\n",
        "    term = \"short\",\n",
        "    n_layers=2,\n",
        "    top_k=5,\n",
        "    d_model=32,\n",
        "    d_ffn=32,\n",
        "    moving_avg=25,\n",
        "    downsampling_window=2,\n",
        "    downsampling_layers=1,\n",
        "    use_norm=True,\n",
        "    dropout=0.1,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/forecasting/timemixer\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "timemixer.fit(train_set=dataset_for_FORE_training, val_set=dataset_for_FORE_validating)\n",
        "# BTTF does not need to run func fits().\n",
        "\n",
        "# the testing stage\n",
        "timemixer_results = timemixer.predict(dataset_for_FORE_testing)\n",
        "timemixer_prediction = timemixer_results[\"forecasting\"]\n",
        "\n",
        "# calculate the mean absolute error on the ground truth in the forecasting task\n",
        "testing_mae = calc_mae(\n",
        "    timemixer_prediction,\n",
        "    np.nan_to_num(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:]),\n",
        "    (~np.isnan(physionet2012_dataset['test_X'][:, -N_PRED_STEPS:])).astype(int),\n",
        ")\n",
        "print(f\"Testing mean absolute error: {testing_mae:.4f}\")\n"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "PnNGplDi_H09"
      },
      "source": [
        "## 🌟 Classification Models"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "JsC9rHnpsuCH"
      },
      "source": [
        "### 💿 Assemble the dateset for the classficiation task"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "Hm5IMCa1-8WW"
      },
      "outputs": [],
      "source": [
        "# Assemble the datasets for training, validating, and testing.\n",
        "\n",
        "dataset_for_CLAS_training = {\n",
        "    \"X\": physionet2012_dataset['train_X'],\n",
        "    \"y\": physionet2012_dataset['train_y'],\n",
        "}\n",
        "\n",
        "dataset_for_CLAS_validating = {\n",
        "    \"X\": physionet2012_dataset['val_X'],\n",
        "    \"y\": physionet2012_dataset['val_y'],\n",
        "}\n",
        "\n",
        "dataset_for_CLAS_testing = {\n",
        "    \"X\": physionet2012_dataset['test_X'],\n",
        "    \"y\": physionet2012_dataset['test_y'],\n",
        "}"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "SASFe4Ro4Fjc"
      },
      "source": [
        "### 🚀 An example of [**TimesNet**](https://arxiv.org/abs/2210.02186) for classification"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "VW3CAdag4Kbw"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.classification import TimesNet\n",
        "from pypots.utils.metrics import calc_binary_classification_metrics\n",
        "\n",
        "# initialize the model\n",
        "timesnet = TimesNet(\n",
        "    n_steps=physionet2012_dataset['n_steps'],\n",
        "    n_features=physionet2012_dataset['n_features'],\n",
        "    n_classes=physionet2012_dataset[\"n_classes\"],\n",
        "    n_layers=2,\n",
        "    top_k=3,\n",
        "    d_model=32,\n",
        "    d_ffn=32,\n",
        "    n_kernels=3,\n",
        "    dropout=0.1,\n",
        "    batch_size=32,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/classification/timesnet\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "timesnet.fit(\n",
        "    train_set = dataset_for_CLAS_training,\n",
        "    val_set = dataset_for_CLAS_validating,\n",
        ")\n",
        "\n",
        "# the testing stage\n",
        "timesnet_results = timesnet.predict(dataset_for_CLAS_testing)\n",
        "timesnet_prediction = timesnet_results[\"classification\"]\n",
        "\n",
        "# calculate the values of binary classification metrics on the model's prediction\n",
        "metrics = calc_binary_classification_metrics(timesnet_prediction, dataset_for_CLAS_testing[\"y\"])\n",
        "print(\"Testing classification metrics: \\n\"\n",
        "    f'ROC_AUC: {metrics[\"roc_auc\"]}, \\n'\n",
        "    f'PR_AUC: {metrics[\"pr_auc\"]},\\n'\n",
        "    f'F1: {metrics[\"f1\"]},\\n'\n",
        "    f'Precision: {metrics[\"precision\"]},\\n'\n",
        "    f'Recall: {metrics[\"recall\"]},\\n'\n",
        ")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "qlluYv6q_jMn"
      },
      "source": [
        "### 🚀 An example of [**BRITS**](https://arxiv.org/abs/1805.10572) for classification"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "FvxZl33v_jCR"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.classification import BRITS\n",
        "from pypots.utils.metrics import calc_binary_classification_metrics\n",
        "\n",
        "# initialize the model\n",
        "brits = BRITS(\n",
        "    n_steps=physionet2012_dataset['n_steps'],\n",
        "    n_features=physionet2012_dataset['n_features'],\n",
        "    n_classes=physionet2012_dataset[\"n_classes\"],\n",
        "    rnn_hidden_size=256,\n",
        "    batch_size=32,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/classification/brits\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "brits.fit(\n",
        "    train_set = dataset_for_CLAS_training,\n",
        "    val_set = dataset_for_CLAS_validating,\n",
        ")\n",
        "\n",
        "# the testing stage\n",
        "brits_results = brits.predict(dataset_for_CLAS_testing)\n",
        "brits_prediction = brits_results[\"classification\"]\n",
        "\n",
        "# calculate the values of binary classification metrics on the model's prediction\n",
        "metrics = calc_binary_classification_metrics(brits_prediction, dataset_for_CLAS_testing[\"y\"])\n",
        "print(\"Testing classification metrics: \\n\"\n",
        "    f'ROC_AUC: {metrics[\"roc_auc\"]}, \\n'\n",
        "    f'PR_AUC: {metrics[\"pr_auc\"]},\\n'\n",
        "    f'F1: {metrics[\"f1\"]},\\n'\n",
        "    f'Precision: {metrics[\"precision\"]},\\n'\n",
        "    f'Recall: {metrics[\"recall\"]},\\n'\n",
        ")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "H4bU3eZY_-01"
      },
      "source": [
        "## 🌟 Clustering Models"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "-LQL-X3tsgbe"
      },
      "source": [
        "### 💿 Assemble the dateset for the clustering task"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "udnvIVuUADBU"
      },
      "outputs": [],
      "source": [
        "# Assemble the datasets for training, validating, and testing.\n",
        "import numpy as np\n",
        "\n",
        "# don't need validation set\n",
        "dataset_for_CLUS_training = {\n",
        "    \"X\": np.concatenate([physionet2012_dataset['train_X'], physionet2012_dataset['val_X']], axis=0),\n",
        "    \"y\": np.concatenate([physionet2012_dataset['train_y'], physionet2012_dataset['val_y']], axis=0),\n",
        "}\n",
        "\n",
        "dataset_for_CLUS_testing = {\n",
        "    \"X\": physionet2012_dataset['test_X'],\n",
        "    \"y\": physionet2012_dataset['test_y'],\n",
        "}\n"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "QvyVryEwAEEk"
      },
      "source": [
        "### 🚀 An example of [**CRLI**](https://ojs.aaai.org/index.php/AAAI/article/view/17070) for clustering"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "SjdHa-fiAJbP"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.clustering import CRLI\n",
        "from pypots.utils.metrics import calc_rand_index, calc_cluster_purity\n",
        "\n",
        "# initialize the model\n",
        "crli = CRLI(\n",
        "    n_steps=physionet2012_dataset[\"n_steps\"],\n",
        "    n_features=physionet2012_dataset[\"n_features\"],\n",
        "    n_clusters=physionet2012_dataset[\"n_classes\"],\n",
        "    n_generator_layers=2,\n",
        "    rnn_hidden_size=256,\n",
        "    rnn_cell_type=\"GRU\",\n",
        "    decoder_fcn_output_dims=[256, 128],  # the output dimensions of layers in the decoder FCN.\n",
        "    # Here means there are 3 layers. Leave it to default as None will results in\n",
        "    # the FCN haveing only one layer.\n",
        "    batch_size=32,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    G_optimizer=Adam(lr=1e-3),\n",
        "    D_optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/clustering/crli\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "crli.fit(train_set=dataset_for_CLUS_training)\n",
        "\n",
        "# the testing stage\n",
        "crli_results = crli.predict(dataset_for_CLUS_testing)\n",
        "crli_prediction = crli_results[\"clustering\"]\n",
        "\n",
        "# calculate the values of clustering metrics on the model's prediction\n",
        "RI = calc_rand_index(crli_prediction, dataset_for_CLUS_testing[\"y\"])\n",
        "CP = calc_cluster_purity(crli_prediction, dataset_for_CLUS_testing[\"y\"])\n",
        "\n",
        "print(\"Testing clustering metrics: \\n\"\n",
        "      f'RI: {RI}, \\n'\n",
        "      f'CP: {CP}\\n'\n",
        ")\n"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "UhoovboEATo7"
      },
      "source": [
        "### 🚀 An example of [**VaDER**](https://academic.oup.com/gigascience/article/8/11/giz134/5626377) for clustering"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "HN9kmDtoAWuk"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.clustering import VaDER\n",
        "from pypots.utils.metrics import calc_rand_index, calc_cluster_purity\n",
        "\n",
        "# initialize the model\n",
        "vader = VaDER(\n",
        "    n_steps=physionet2012_dataset[\"n_steps\"],\n",
        "    n_features=physionet2012_dataset[\"n_features\"],\n",
        "    n_clusters=physionet2012_dataset[\"n_classes\"],\n",
        "    rnn_hidden_size=128,\n",
        "    d_mu_stddev=2,\n",
        "    pretrain_epochs=20,\n",
        "    batch_size=32,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/clustering/vader\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "# train the model on the training set, and validate it on the validating set to select the best model for testing in the next step\n",
        "vader.fit(train_set=dataset_for_CLUS_training)\n",
        "\n",
        "# the testing stage\n",
        "vader_results = vader.predict(dataset_for_CLUS_testing)\n",
        "vader_prediction = vader_results[\"clustering\"]\n",
        "\n",
        "# calculate the values of clustering metrics on the model's prediction\n",
        "RI = calc_rand_index(vader_prediction, dataset_for_CLUS_testing[\"y\"])\n",
        "CP = calc_cluster_purity(vader_prediction, dataset_for_CLUS_testing[\"y\"])\n",
        "\n",
        "print(\"Testing clustering metrics: \\n\"\n",
        "      f'RI: {RI}, \\n'\n",
        "      f'CP: {CP},\\n'\n",
        ")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "PVFe-Try43yh"
      },
      "source": [
        "## 🌟 Anomaly Detection Models"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "sR5DIqvpiTuK"
      },
      "source": [
        "### 📀 Preparing a synthetic dataset for anomaly detection\n",
        "\n",
        "#### 🤔 Due to physionet-2012 does not have anomaly detection groud-truth labels in test set for model evalution, we generate a random walk dataset with anomalies here"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "SXiobXeviVUI"
      },
      "outputs": [],
      "source": [
        "from benchpots.datasets import preprocess_random_walk\n",
        "\n",
        "N_STEPS = 6\n",
        "N_PRED_STEPS = 2\n",
        "N_FEATURES = 5\n",
        "ANOMALY_RATE = 0.05\n",
        "MISSING_RATE = 0.1\n",
        "\n",
        "ANDO_dataset = preprocess_random_walk(\n",
        "    n_steps=N_STEPS + N_PRED_STEPS,  # the total sequence length\n",
        "    n_features=N_FEATURES,\n",
        "    anomaly_rate=ANOMALY_RATE,\n",
        "    missing_rate=MISSING_RATE,\n",
        ")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "5TXfVWaS4-w0"
      },
      "source": [
        "### 💿 Assemble the dateset for the anomaly detection task"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "JmgTT2fS5BJo"
      },
      "outputs": [],
      "source": [
        "# Assemble the datasets for training, validating, and testing.\n",
        "\n",
        "dataset_for_ANOD_training = {\n",
        "    \"X\": ANDO_dataset['train_X'],\n",
        "    \"anomaly_y\": ANDO_dataset[\"train_anomaly_y\"].astype(float),\n",
        "}\n",
        "\n",
        "dataset_for_ANOD_validating = {\n",
        "    \"X\": ANDO_dataset['val_X'],\n",
        "    \"X_ori\": ANDO_dataset[\"val_X_ori\"],\n",
        "    \"anomaly_y\": ANDO_dataset[\"val_anomaly_y\"].astype(float),\n",
        "}\n",
        "\n",
        "dataset_for_ANOD_testing = {\n",
        "    \"X\": ANDO_dataset['test_X'],\n",
        "    \"X_ori\": ANDO_dataset[\"test_X_ori\"],\n",
        "    \"anomaly_y\": ANDO_dataset[\"test_anomaly_y\"].astype(float), #\n",
        "}"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "IOTnFaGb5Ydr"
      },
      "source": [
        "### 🚀 An example of [**Autoformer**](https://arxiv.org/abs/2106.13008) for anomaly detection"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "LRMe1cGB5flC"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.anomaly_detection import Autoformer\n",
        "from pypots.nn.functional import calc_acc, calc_precision_recall_f1\n",
        "\n",
        "autoformer = Autoformer(\n",
        "    n_steps = ANDO_dataset[\"n_steps\"],\n",
        "    n_features = ANDO_dataset[\"n_features\"],\n",
        "    anomaly_rate = ANOMALY_RATE,\n",
        "    n_layers=2,\n",
        "    n_heads=2,\n",
        "    d_model=32,\n",
        "    d_ffn=32,\n",
        "    factor=3,\n",
        "    moving_avg_window_size=3,\n",
        "    dropout=0,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/anomaly_detection/autoformer\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "autoformer.fit(dataset_for_ANOD_training, dataset_for_ANOD_validating)\n",
        "\n",
        "anomaly_detection_results = autoformer.predict(dataset_for_ANOD_testing)\n",
        "anomaly_labels = dataset_for_ANOD_testing[\"anomaly_y\"].flatten()\n",
        "accuracy = calc_acc(\n",
        "    anomaly_detection_results[\"anomaly_detection\"],\n",
        "    anomaly_labels,\n",
        ")\n",
        "precision, recall, f1 = calc_precision_recall_f1(\n",
        "    anomaly_detection_results[\"anomaly_detection\"],\n",
        "    anomaly_labels,\n",
        ")\n",
        "\n",
        "print(f\"Autoformer Accuracy: {accuracy}, F1: {f1}, Precision: {precision}, Recall: {recall}\")"
      ]
    },
    {
      "cell_type": "markdown",
      "metadata": {
        "id": "9gVMLDgx64Bl"
      },
      "source": [
        "### 🚀 An example of [**PatchTST**](https://arxiv.org/abs/2211.14730) for anomaly detection"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "id": "W-GA9_lv68M4"
      },
      "outputs": [],
      "source": [
        "from pypots.optim import Adam\n",
        "from pypots.anomaly_detection import PatchTST\n",
        "from pypots.nn.functional import calc_acc, calc_precision_recall_f1\n",
        "\n",
        "patchtst = PatchTST(\n",
        "    n_steps = ANDO_dataset[\"n_steps\"],\n",
        "    n_features = ANDO_dataset[\"n_features\"],\n",
        "    anomaly_rate = ANOMALY_RATE,\n",
        "    n_layers=2,\n",
        "    d_model=64,\n",
        "    n_heads=2,\n",
        "    d_k=16,\n",
        "    d_v=16,\n",
        "    d_ffn=32,\n",
        "    patch_size=ANDO_dataset[\"n_steps\"],\n",
        "    patch_stride=8,\n",
        "    dropout=0.1,\n",
        "    attn_dropout=0,\n",
        "    # here we set epochs=10 for a quick demo, you can set it to 100 or more for better performance\n",
        "    epochs=10,\n",
        "    # here we set patience=3 to early stop the training if the evaluting loss doesn't decrease for 3 epoches.\n",
        "    # You can leave it to defualt as None to disable early stopping.\n",
        "    patience=3,\n",
        "    # give the optimizer. Different from torch.optim.Optimizer, you don't have to specify model's parameters when\n",
        "    # initializing pypots.optim.Optimizer. You can also leave it to default. It will initilize an Adam optimizer with lr=0.001.\n",
        "    optimizer=Adam(lr=1e-3),\n",
        "    # this num_workers argument is for torch.utils.data.Dataloader. It's the number of subprocesses to use for data loading.\n",
        "    # Leaving it to default as 0 means data loading will be in the main process, i.e. there won't be subprocesses.\n",
        "    # You can increase it to >1 if you think your dataloading is a bottleneck to your model training speed\n",
        "    num_workers=0,\n",
        "    # just leave it to default as None, PyPOTS will automatically assign the best device for you.\n",
        "    # Set it as 'cpu' if you don't have CUDA devices. You can also set it to 'cuda:0' or 'cuda:1' if you have multiple CUDA devices, even parallelly on ['cuda:0', 'cuda:1']\n",
        "    device=None,\n",
        "    # set the path for saving tensorboard and trained model files\n",
        "    saving_path=\"tutorial_results/anomaly_detection/patchtst\",\n",
        "    # only save the best model after training finished.\n",
        "    # You can also set it as \"better\" to save models performing better ever during training.\n",
        "    model_saving_strategy=\"best\",\n",
        ")\n",
        "\n",
        "patchtst.fit(dataset_for_ANOD_training, dataset_for_ANOD_validating)\n",
        "\n",
        "anomaly_detection_results = patchtst.predict(dataset_for_ANOD_testing)\n",
        "anomaly_labels = dataset_for_ANOD_testing[\"anomaly_y\"].flatten()\n",
        "accuracy = calc_acc(\n",
        "    anomaly_detection_results[\"anomaly_detection\"],\n",
        "    anomaly_labels,\n",
        ")\n",
        "precision, recall, f1 = calc_precision_recall_f1(\n",
        "    anomaly_detection_results[\"anomaly_detection\"],\n",
        "    anomaly_labels,\n",
        ")\n",
        "\n",
        "print(f\"PatchTST Accuracy: {accuracy}, F1: {f1}, Precision: {precision}, Recall: {recall}\")"
      ]
    },
    {
      "cell_type": "code",
      "execution_count": null,
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/"
        },
        "id": "6dc50cfa",
        "outputId": "062b3c31-4f62-4f1e-c6ff-fff4f5c5d3a9"
      },
      "outputs": [
        {
          "name": "stdout",
          "output_type": "stream",
          "text": [
            "Collecting benchpots\n",
            "  Downloading benchpots-0.4-py3-none-any.whl.metadata (9.5 kB)\n",
            "Requirement already satisfied: h5py in /usr/local/lib/python3.11/dist-packages (from benchpots) (3.14.0)\n",
            "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from benchpots) (2.0.2)\n",
            "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from benchpots) (2.2.2)\n",
            "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (from benchpots) (1.6.1)\n",
            "Requirement already satisfied: torch>=1.10 in /usr/local/lib/python3.11/dist-packages (from benchpots) (2.6.0+cu124)\n",
            "Collecting tsdb>=0.7.1 (from benchpots)\n",
            "  Downloading tsdb-0.7.1-py3-none-any.whl.metadata (13 kB)\n",
            "Collecting pygrinder>=0.7 (from benchpots)\n",
            "  Downloading pygrinder-0.7-py3-none-any.whl.metadata (10 kB)\n",
            "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (3.18.0)\n",
            "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (4.14.1)\n",
            "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (3.5)\n",
            "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (3.1.6)\n",
            "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (2025.7.0)\n",
            "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n",
            "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n",
            "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n",
            "Collecting nvidia-cudnn-cu12==9.1.0.70 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cudnn_cu12-9.1.0.70-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n",
            "Collecting nvidia-cublas-cu12==12.4.5.8 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cublas_cu12-12.4.5.8-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n",
            "Collecting nvidia-cufft-cu12==11.2.1.3 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cufft_cu12-11.2.1.3-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n",
            "Collecting nvidia-curand-cu12==10.3.5.147 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_curand_cu12-10.3.5.147-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n",
            "Collecting nvidia-cusolver-cu12==11.6.1.9 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cusolver_cu12-11.6.1.9-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n",
            "Collecting nvidia-cusparse-cu12==12.3.1.170 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_cusparse_cu12-12.3.1.170-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n",
            "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (0.6.2)\n",
            "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (2.21.5)\n",
            "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (12.4.127)\n",
            "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch>=1.10->benchpots)\n",
            "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n",
            "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (3.2.0)\n",
            "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=1.10->benchpots) (1.13.1)\n",
            "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=1.10->benchpots) (1.3.0)\n",
            "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from tsdb>=0.7.1->benchpots) (4.67.1)\n",
            "Requirement already satisfied: scipy in /usr/local/lib/python3.11/dist-packages (from tsdb>=0.7.1->benchpots) (1.16.0)\n",
            "Requirement already satisfied: pyarrow in /usr/local/lib/python3.11/dist-packages (from tsdb>=0.7.1->benchpots) (18.1.0)\n",
            "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from tsdb>=0.7.1->benchpots) (2.32.3)\n",
            "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->benchpots) (2.9.0.post0)\n",
            "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->benchpots) (2025.2)\n",
            "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->benchpots) (2025.2)\n",
            "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->benchpots) (1.5.1)\n",
            "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn->benchpots) (3.6.0)\n",
            "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->benchpots) (1.17.0)\n",
            "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=1.10->benchpots) (3.0.2)\n",
            "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->tsdb>=0.7.1->benchpots) (3.4.2)\n",
            "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->tsdb>=0.7.1->benchpots) (3.10)\n",
            "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->tsdb>=0.7.1->benchpots) (2.5.0)\n",
            "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->tsdb>=0.7.1->benchpots) (2025.7.14)\n",
            "Downloading benchpots-0.4-py3-none-any.whl (30 kB)\n",
            "Downloading pygrinder-0.7-py3-none-any.whl (24 kB)\n",
            "Downloading nvidia_cublas_cu12-12.4.5.8-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m100.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m80.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m50.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_cudnn_cu12-9.1.0.70-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_cufft_cu12-11.2.1.3-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_curand_cu12-10.3.5.147-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m12.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_cusolver_cu12-11.6.1.9-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_cusparse_cu12-12.3.1.170-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m5.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n",
            "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m87.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n",
            "\u001b[?25hDownloading tsdb-0.7.1-py3-none-any.whl (32 kB)\n",
            "Installing collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, tsdb, nvidia-cusolver-cu12, pygrinder, benchpots\n",
            "  Attempting uninstall: nvidia-nvjitlink-cu12\n",
            "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n",
            "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n",
            "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n",
            "  Attempting uninstall: nvidia-curand-cu12\n",
            "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n",
            "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n",
            "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n",
            "  Attempting uninstall: nvidia-cufft-cu12\n",
            "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n",
            "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n",
            "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n",
            "  Attempting uninstall: nvidia-cuda-runtime-cu12\n",
            "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n",
            "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n",
            "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n",
            "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n",
            "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n",
            "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n",
            "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n",
            "  Attempting uninstall: nvidia-cuda-cupti-cu12\n",
            "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n",
            "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n",
            "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n",
            "  Attempting uninstall: nvidia-cublas-cu12\n",
            "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n",
            "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n",
            "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n",
            "  Attempting uninstall: nvidia-cusparse-cu12\n",
            "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n",
            "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n",
            "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n",
            "  Attempting uninstall: nvidia-cudnn-cu12\n",
            "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n",
            "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n",
            "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n",
            "  Attempting uninstall: nvidia-cusolver-cu12\n",
            "    Found existing installation: nvidia-cusolver-cu12 11.6.3.83\n",
            "    Uninstalling nvidia-cusolver-cu12-11.6.3.83:\n",
            "      Successfully uninstalled nvidia-cusolver-cu12-11.6.3.83\n",
            "\u001b[31mERROR: Operation cancelled by user\u001b[0m\u001b[31m\n",
            "\u001b[0m"
          ]
        }
      ],
      "source": [
        "# Package installation handled above"
      ]
    }
  ],
  "metadata": {
    "colab": {
      "collapsed_sections": [
        "sR5DIqvpiTuK",
        "5TXfVWaS4-w0",
        "IOTnFaGb5Ydr",
        "9gVMLDgx64Bl"
      ],
      "provenance": []
    },
    "kernelspec": {
      "display_name": "Python 3",
      "name": "python3"
    },
    "language_info": {
      "name": "python"
    }
  },
  "nbformat": 4,
  "nbformat_minor": 0
}
