"""
GPU Fallback Utilities for ML Log Prediction
Provides robust fallback mechanisms when GPU operations fail.
"""

import torch
import warnings
from typing import Any, Callable, Optional, Dict, Union
from functools import wraps
import traceback

class GPUFallbackManager:
    """
    Manages GPU fallback operations with automatic CPU fallback.
    """
    
    def __init__(self, enable_warnings=True):
        """
        Initialize GPU fallback manager.
        
        Args:
            enable_warnings: Whether to show fallback warnings
        """
        self.enable_warnings = enable_warnings
        self.fallback_count = 0
        self.gpu_available = torch.cuda.is_available()
        self.fallback_reasons = []
        
        if not self.gpu_available:
            self._warn("CUDA not available, all operations will use CPU")
    
    def _warn(self, message: str):
        """Issue a warning if warnings are enabled."""
        if self.enable_warnings:
            warnings.warn(f"GPU Fallback: {message}", UserWarning)
            print(f"⚠️ {message}")
    
    def _log_fallback(self, reason: str, operation: str = "unknown"):
        """Log a fallback occurrence."""
        self.fallback_count += 1
        self.fallback_reasons.append(f"{operation}: {reason}")
        self._warn(f"Falling back to CPU for {operation} - {reason}")
    
    def safe_to_device(self, tensor: torch.Tensor, device: Union[str, torch.device], 
                      operation_name: str = "tensor transfer") -> torch.Tensor:
        """
        Safely move tensor to device with automatic CPU fallback.
        
        Args:
            tensor: Tensor to move
            device: Target device
            operation_name: Name of the operation for logging
            
        Returns:
            Tensor on the target device or CPU if fallback occurred
        """
        if not isinstance(device, torch.device):
            device = torch.device(device)
        
        # If target is CPU or GPU not available, return CPU tensor
        if device.type == 'cpu' or not self.gpu_available:
            return tensor.cpu()
        
        try:
            return tensor.to(device)
        except Exception as e:
            self._log_fallback(str(e), operation_name)
            return tensor.cpu()
    
    def safe_model_to_device(self, model: torch.nn.Module, device: Union[str, torch.device],
                           operation_name: str = "model transfer") -> torch.nn.Module:
        """
        Safely move model to device with automatic CPU fallback.
        
        Args:
            model: Model to move
            device: Target device
            operation_name: Name of the operation for logging
            
        Returns:
            Model on the target device or CPU if fallback occurred
        """
        if not isinstance(device, torch.device):
            device = torch.device(device)
        
        # If target is CPU or GPU not available, return CPU model
        if device.type == 'cpu' or not self.gpu_available:
            return model.cpu()
        
        try:
            return model.to(device)
        except Exception as e:
            self._log_fallback(str(e), operation_name)
            return model.cpu()
    
    def safe_gpu_operation(self, operation: Callable, *args, fallback_operation: Optional[Callable] = None,
                          operation_name: str = "GPU operation", **kwargs) -> Any:
        """
        Execute operation with automatic CPU fallback.
        
        Args:
            operation: Primary operation to execute
            *args: Arguments for the operation
            fallback_operation: Alternative CPU operation (optional)
            operation_name: Name of the operation for logging
            **kwargs: Keyword arguments for the operation
            
        Returns:
            Result of the operation (GPU or CPU fallback)
        """
        if not self.gpu_available:
            if fallback_operation:
                return fallback_operation(*args, **kwargs)
            else:
                return operation(*args, **kwargs)
        
        try:
            return operation(*args, **kwargs)
        except Exception as e:
            self._log_fallback(str(e), operation_name)
            
            if fallback_operation:
                try:
                    return fallback_operation(*args, **kwargs)
                except Exception as fallback_e:
                    self._warn(f"Fallback operation also failed: {fallback_e}")
                    raise fallback_e
            else:
                # Try to modify args to use CPU
                try:
                    cpu_args = []
                    for arg in args:
                        if isinstance(arg, torch.Tensor):
                            cpu_args.append(arg.cpu())
                        else:
                            cpu_args.append(arg)
                    
                    cpu_kwargs = {}
                    for key, value in kwargs.items():
                        if isinstance(value, torch.Tensor):
                            cpu_kwargs[key] = value.cpu()
                        elif key == 'device':
                            cpu_kwargs[key] = 'cpu'
                        else:
                            cpu_kwargs[key] = value
                    
                    return operation(*cpu_args, **cpu_kwargs)
                except Exception as cpu_e:
                    self._warn(f"CPU fallback also failed: {cpu_e}")
                    raise cpu_e
    
    def get_safe_device(self, preferred_device: Union[str, torch.device] = 'cuda') -> torch.device:
        """
        Get a safe device with automatic fallback.
        
        Args:
            preferred_device: Preferred device
            
        Returns:
            Safe device to use
        """
        if not isinstance(preferred_device, torch.device):
            preferred_device = torch.device(preferred_device)
        
        if preferred_device.type == 'cpu':
            return preferred_device
        
        if not self.gpu_available:
            self._log_fallback("CUDA not available", "device selection")
            return torch.device('cpu')
        
        try:
            # Test GPU availability with a simple operation
            test_tensor = torch.tensor([1.0]).to(preferred_device)
            _ = test_tensor + 1
            return preferred_device
        except Exception as e:
            self._log_fallback(str(e), "device selection")
            return torch.device('cpu')
    
    def create_safe_scaler(self, device: Union[str, torch.device]) -> Optional[Any]:
        """
        Create a safe gradient scaler for mixed precision.
        
        Args:
            device: Target device
            
        Returns:
            GradScaler if GPU is available and working, None otherwise
        """
        if not isinstance(device, torch.device):
            device = torch.device(device)
        
        if device.type == 'cpu' or not self.gpu_available:
            return None
        
        try:
            # Try new API first
            try:
                scaler = torch.amp.GradScaler('cuda')
            except AttributeError:
                # Fallback to old API
                scaler = torch.cuda.amp.GradScaler()
            
            # Test the scaler
            test_tensor = torch.tensor([1.0], requires_grad=True).to(device)
            scaled = scaler.scale(test_tensor)
            return scaler
        except Exception as e:
            self._log_fallback(str(e), "gradient scaler creation")
            return None
    
    def safe_autocast_context(self, device: Union[str, torch.device]):
        """
        Create a safe autocast context with fallback.
        
        Args:
            device: Target device
            
        Returns:
            Autocast context or dummy context
        """
        if not isinstance(device, torch.device):
            device = torch.device(device)
        
        if device.type == 'cpu' or not self.gpu_available:
            # Return a dummy context for CPU
            from contextlib import nullcontext
            return nullcontext()
        
        try:
            # Try new API first
            try:
                return torch.amp.autocast('cuda')
            except AttributeError:
                # Fallback to old API
                return torch.cuda.amp.autocast()
        except Exception as e:
            self._log_fallback(str(e), "autocast context creation")
            from contextlib import nullcontext
            return nullcontext()
    
    def get_fallback_summary(self) -> Dict[str, Any]:
        """
        Get summary of fallback operations.
        
        Returns:
            Dictionary with fallback statistics
        """
        return {
            'total_fallbacks': self.fallback_count,
            'gpu_available': self.gpu_available,
            'fallback_reasons': self.fallback_reasons.copy(),
            'unique_reasons': list(set([reason.split(':')[1].strip() for reason in self.fallback_reasons]))
        }
    
    def print_fallback_report(self):
        """Print a summary of fallback operations."""
        summary = self.get_fallback_summary()
        
        print("\n" + "="*50)
        print("🔄 GPU FALLBACK REPORT")
        print("="*50)
        print(f"GPU Available: {'✅' if summary['gpu_available'] else '❌'}")
        print(f"Total Fallbacks: {summary['total_fallbacks']}")
        
        if summary['fallback_reasons']:
            print("\nFallback Reasons:")
            for reason in summary['unique_reasons']:
                count = sum(1 for r in summary['fallback_reasons'] if reason in r)
                print(f"  • {reason}: {count} times")
        else:
            print("✅ No fallbacks occurred")
        
        print("="*50)

# Global fallback manager instance
_global_fallback_manager = None

def get_fallback_manager() -> GPUFallbackManager:
    """Get the global fallback manager instance."""
    global _global_fallback_manager
    if _global_fallback_manager is None:
        _global_fallback_manager = GPUFallbackManager()
    return _global_fallback_manager

def gpu_fallback_decorator(operation_name: str = "decorated operation"):
    """
    Decorator for automatic GPU fallback on functions.
    
    Args:
        operation_name: Name of the operation for logging
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            manager = get_fallback_manager()
            return manager.safe_gpu_operation(func, *args, operation_name=operation_name, **kwargs)
        return wrapper
    return decorator

def safe_cuda_empty_cache():
    """Safely empty CUDA cache with fallback."""
    try:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    except Exception as e:
        manager = get_fallback_manager()
        manager._log_fallback(str(e), "CUDA cache clearing")

def print_fallback_report():
    """Print global fallback report."""
    manager = get_fallback_manager()
    manager.print_fallback_report()
