
# Codebase Overview

This document provides a high-level overview of the codebase structure, key components, and development phases.

## 1. Core Pipeline

The main ML pipeline is orchestrated by the following core components:

| File | Description |
|---|---|
| `main.py` | Main entry point with an interactive GUI workflow. |
| `data_handler.py` | Handles LAS file operations, data loading, cleaning, and preprocessing. |
| `ml_core.py` | Contains the model registry and machine learning prediction pipelines. |
| `config_handler.py` | Manages user interfaces, file selection, and configuration settings. |
| `reporting.py` | Generates all visualizations, QC reports, and performance analysis. |

## 2. Model Implementations

The project includes a comprehensive set of models, organized as follows:

- **Shallow Models**: Gradient Boosting (XGBoost, LightGBM, CatBoost) and statistical models (Linear, Ridge, Lasso, ElasticNet).
- **Deep Learning Models**:
    - **Basic**: `models/simple_autoencoder.py` (Autoencoder, U-Net)
    - **Advanced**: `models/advanced_models/` (SAITS, BRITS, Transformer, MRNN) - *Currently under development (Phase 2)*

## 3. Utilities

Specialized utility modules enhance the pipeline's functionality:

| Directory | Description |
|---|---|
| `utils/` | GPU acceleration, memory optimization, performance monitoring, and advanced visualization. |
| `mlr_utils.py` | Utilities for Multiple Linear Regression models. |
| `data_leakage_detector.py` | Tools for detecting data leakage in ML models. |

## 4. Development Phases

The project is being developed in four phases:

- **Phase 1 (Completed)**: Focused on memory optimization, GPU acceleration, and basic model stability.
- **Phase 2 (In Progress)**: Implementation of advanced deep learning models (SAITS, BRITS, Transformer).
- **Phase 3 (Planned)**: Enhanced preprocessing, validation, and testing.
- **Phase 4 (Planned)**: Final performance optimization and deployment preparation.

## 5. Documentation and Archives

- **`docs/`**: Contains historical analysis, implementation summaries, and legacy documentation.
- **`archives/`**: Stores historical code, debug scripts, and experiment results.
- **`README.md`**: The main project documentation.
- **`List_of_cleaned_file.md`**: A log of the codebase cleanup performed on 2025-01-27.

