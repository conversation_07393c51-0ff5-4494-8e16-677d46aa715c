import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# Import display utilities for emoji handling and font management
from utils.display_utils import (
    configure_display, get_ranking_symbol, format_ranking_text, 
    add_ranking_annotation, print_ranking_summary
)

# Configure display settings for cross-platform emoji support
display_manager = configure_display(
    prefer_emoji=True,
    fallback_mode='text',
    suppress_warnings=True
)

def generate_qc_report(df, logs, cfg):
    """Generate quality control report with enhanced visualization."""
    print("\nCoverage:")
    cov = 1 - df[logs].isna().mean()
    for l,c in cov.items():
        print(f"  {l}: {c:.1%}")

    # Enhanced QC visualization
    if len(logs) > 1:
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        coverage_data = [(log, cov[log]) for log in logs]
        coverage_data.sort(key=lambda x: x[1], reverse=True)

        log_names, coverage_values = zip(*coverage_data)
        bars = ax.bar(range(len(log_names)), coverage_values,
                     color=['green' if c >= 0.8 else 'orange' if c >= 0.6 else 'red' for c in coverage_values])

        ax.set_xlabel('Log Curves')
        ax.set_ylabel('Data Coverage (%)')
        ax.set_title('Data Quality Report - Log Coverage Analysis')
        ax.set_xticks(range(len(log_names)))
        ax.set_xticklabels(log_names, rotation=45, ha='right')
        ax.set_ylim(0, 1)
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: f'{y:.0%}'))

        # Add coverage threshold lines
        ax.axhline(y=0.8, color='green', linestyle='--', alpha=0.7, label='Good Coverage (≥80%)')
        ax.axhline(y=0.6, color='orange', linestyle='--', alpha=0.7, label='Fair Coverage (≥60%)')

        # Add value labels on bars
        for bar, value in zip(bars, coverage_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.1%}', ha='center', va='bottom', fontsize=9)

        ax.legend(loc='upper right')
        ax.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        plt.show()

def create_summary_plots(res_df, model_res, cfg, model_name=None, show_error_bands=False):
    """
    Create enhanced summary plots with comprehensive legends and model information.

    Parameters:
    -----------
    res_df : pandas.DataFrame
        Results dataframe with predictions
    model_res : dict
        Model results dictionary containing evaluations and metadata
    cfg : dict
        Configuration dictionary
    model_name : str, optional
        Name of the model for display in plots. If None, extracted from model_res
    show_error_bands : bool, optional
        Whether to show error bands around predictions
    """
    tgt = model_res['target']
    wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()
    wells = wells[:6]  # Limit to 6 wells for readability

    # Extract model name for display
    if model_name is None:
        model_name = model_res.get('best_model_name', 'Unknown Model')
        if 'evaluations' in model_res and model_res['evaluations']:
            model_name = model_res['evaluations'][0].get('model_name', model_name)

    # Create figure with enhanced layout
    fig_width = max(4*len(wells), 12)  # Minimum width for legend visibility
    fig, axes = plt.subplots(1, len(wells), figsize=(fig_width, 10), sharey=True)
    if len(wells) == 1:
        axes = [axes]

    # Define consistent colors and styles
    colors = {
        'original': '#2E86AB',      # Blue
        'imputed': '#A23B72',       # Purple/Magenta
        'predicted': '#F18F01',     # Orange
        'error': '#C73E1D'          # Red
    }

    line_styles = {
        'original': '-',            # Solid line
        'imputed': '--',            # Dashed line
        'predicted': ':',           # Dotted line
        'error': '-.'               # Dash-dot line
    }

    # Plot data for each well
    for ax, w in zip(axes, wells):
        d = res_df[res_df['WELL'] == w].copy()

        if d.empty:
            ax.set_title(f"{w}\n(No Data)")
            continue

        # Plot original data
        original_mask = d[tgt].notna()
        if original_mask.any():
            ax.plot(d.loc[original_mask, tgt], d.loc[original_mask, 'MD'],
                   color=colors['original'], linestyle=line_styles['original'],
                   linewidth=2, label='Original Data', alpha=0.8)

        # Plot imputed data
        imputed_col = f"{tgt}_imputed"
        if imputed_col in d.columns:
            imputed_mask = d[imputed_col].notna()
            if imputed_mask.any():
                ax.plot(d.loc[imputed_mask, imputed_col], d.loc[imputed_mask, 'MD'],
                       color=colors['imputed'], linestyle=line_styles['imputed'],
                       linewidth=2, label=f'{model_name} Imputed', alpha=0.8)

        # Plot predicted data (if different from imputed)
        pred_col = f"{tgt}_pred"
        if pred_col in d.columns:
            pred_mask = d[pred_col].notna()
            if pred_mask.any():
                ax.plot(d.loc[pred_mask, pred_col], d.loc[pred_mask, 'MD'],
                       color=colors['predicted'], linestyle=line_styles['predicted'],
                       linewidth=1.5, label=f'{model_name} Predicted', alpha=0.7)

        # Add error bands if requested
        if show_error_bands and pred_col in d.columns and original_mask.any():
            error_data = d.loc[original_mask & d[pred_col].notna()]
            if not error_data.empty:
                errors = np.abs(error_data[tgt] - error_data[pred_col])
                mean_error = errors.mean()

                # Create error bands around predictions
                pred_values = d.loc[pred_mask, pred_col]
                md_values = d.loc[pred_mask, 'MD']

                ax.fill_betweenx(md_values,
                               pred_values - mean_error,
                               pred_values + mean_error,
                               color=colors['error'], alpha=0.2,
                               label=f'±{mean_error:.2f} Error Band')

        # Calculate and display metrics
        comp = d[[tgt, imputed_col]].dropna() if imputed_col in d.columns else pd.DataFrame()
        if not comp.empty:
            mae = mean_absolute_error(comp[tgt], comp[imputed_col])
            r2 = r2_score(comp[tgt], comp[imputed_col])

            # Enhanced title with model info and metrics
            title_lines = [
                f"Well: {w}",
                f"Model: {model_name}",
                f"MAE: {mae:.3f} | R²: {r2:.3f}"
            ]
            ax.set_title('\n'.join(title_lines), fontsize=10, pad=20)
        else:
            ax.set_title(f"Well: {w}\nModel: {model_name}\n(No Comparison Data)", fontsize=10, pad=20)

        # Formatting
        ax.invert_yaxis()
        ax.grid(True, alpha=0.3)
        ax.set_xlabel(f'{tgt} Value', fontsize=10)

        # Add well-specific statistics as text box
        if not comp.empty:
            stats_text = f'n={len(comp)}\nMAE={mae:.3f}\nR²={r2:.3f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   verticalalignment='top', bbox=dict(boxstyle='round',
                   facecolor='wheat', alpha=0.8), fontsize=8)

    # Set common y-label
    axes[0].set_ylabel('Measured Depth (MD)', fontsize=12)

    # Create comprehensive legend
    handles, labels = axes[0].get_legend_handles_labels()
    if handles:
        # Position legend outside the plot area
        fig.legend(handles, labels, loc='upper center', bbox_to_anchor=(0.5, 0.95),
                  ncol=min(len(handles), 4), fontsize=11, frameon=True,
                  fancybox=True, shadow=True)

    # Add overall title with model information
    overall_title = f'ML Log Prediction Results - {model_name}\nTarget: {tgt}'
    if 'evaluations' in model_res and model_res['evaluations']:
        eval_info = model_res['evaluations'][0]
        if 'mae' in eval_info and 'r2' in eval_info:
            overall_title += f' | Overall MAE: {eval_info["mae"]:.3f} | R²: {eval_info["r2"]:.3f}'

    fig.suptitle(overall_title, fontsize=14, fontweight='bold', y=0.98)

    plt.tight_layout()
    plt.subplots_adjust(top=0.85)  # Make room for title and legend
    plt.show()

def create_separate_comparison_plots(res_df, model_res, cfg, model_name=None):
    """
    Create separate figure plots for better clarity:
    1. Original vs Imputed comparison
    2. Original vs Predicted comparison

    Parameters:
    -----------
    res_df : pandas.DataFrame
        Results dataframe with predictions
    model_res : dict
        Model results dictionary containing evaluations and metadata
    cfg : dict
        Configuration dictionary
    model_name : str, optional
        Name of the model for display in plots
    """
    tgt = model_res['target']
    wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()
    wells = wells[:6]  # Limit to 6 wells for readability

    # Extract model name for display
    if model_name is None:
        model_name = model_res.get('best_model_name', 'Unknown Model')
        if 'evaluations' in model_res and model_res['evaluations']:
            model_name = model_res['evaluations'][0].get('model_name', model_name)

    # Define consistent colors and styles
    colors = {
        'original': '#2E86AB',      # Blue
        'imputed': '#A23B72',       # Purple/Magenta
        'predicted': '#F18F01',     # Orange
    }

    line_styles = {
        'original': '-',            # Solid line
        'imputed': '--',            # Dashed line
        'predicted': ':',           # Dotted line
    }

    # Common figure setup
    fig_width = max(4*len(wells), 12)

    # === FIGURE 1: Original vs Imputed ===
    fig1, axes1 = plt.subplots(1, len(wells), figsize=(fig_width, 10), sharey=True)
    if len(wells) == 1:
        axes1 = [axes1]

    imputed_col = f"{tgt}_imputed"

    for ax, w in zip(axes1, wells):
        d = res_df[res_df['WELL'] == w].copy()

        if d.empty:
            ax.set_title(f"{w}\n(No Data)")
            continue

        # Plot original data
        original_mask = d[tgt].notna()
        if original_mask.any():
            ax.plot(d.loc[original_mask, tgt], d.loc[original_mask, 'MD'],
                   color=colors['original'], linestyle=line_styles['original'],
                   linewidth=2, label='Original Data', alpha=0.8)

        # Plot imputed data
        if imputed_col in d.columns:
            imputed_mask = d[imputed_col].notna()
            if imputed_mask.any():
                ax.plot(d.loc[imputed_mask, imputed_col], d.loc[imputed_mask, 'MD'],
                       color=colors['imputed'], linestyle=line_styles['imputed'],
                       linewidth=2, label=f'{model_name} Imputed', alpha=0.8)

        # Calculate and display metrics
        comp = d[[tgt, imputed_col]].dropna() if imputed_col in d.columns else pd.DataFrame()
        if not comp.empty:
            mae = mean_absolute_error(comp[tgt], comp[imputed_col])
            r2 = r2_score(comp[tgt], comp[imputed_col])

            title_lines = [
                f"Well: {w}",
                f"MAE: {mae:.3f} | R²: {r2:.3f}"
            ]
            ax.set_title('\n'.join(title_lines), fontsize=10, pad=20)
        else:
            ax.set_title(f"Well: {w}\n(No Comparison Data)", fontsize=10, pad=20)

        # Formatting
        ax.invert_yaxis()
        ax.grid(True, alpha=0.3)
        ax.set_xlabel(f'{tgt} Value', fontsize=10)

    # Set common y-label and legend for Figure 1
    axes1[0].set_ylabel('Measured Depth (MD)', fontsize=12)
    handles1, labels1 = axes1[0].get_legend_handles_labels()
    if handles1:
        fig1.legend(handles1, labels1, loc='upper center', bbox_to_anchor=(0.5, 0.95),
                   ncol=min(len(handles1), 4), fontsize=11, frameon=True,
                   fancybox=True, shadow=True)

    # Title for Figure 1
    fig1.suptitle(f'Original vs Imputed Data - {model_name}\nTarget: {tgt}',
                 fontsize=14, fontweight='bold', y=0.98)

    plt.figure(fig1.number)
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)
    plt.show()

    # === FIGURE 2: Original vs Predicted ===
    fig2, axes2 = plt.subplots(1, len(wells), figsize=(fig_width, 10), sharey=True)
    if len(wells) == 1:
        axes2 = [axes2]

    pred_col = f"{tgt}_pred"

    for ax, w in zip(axes2, wells):
        d = res_df[res_df['WELL'] == w].copy()

        if d.empty:
            ax.set_title(f"{w}\n(No Data)")
            continue

        # Plot original data
        original_mask = d[tgt].notna()
        if original_mask.any():
            ax.plot(d.loc[original_mask, tgt], d.loc[original_mask, 'MD'],
                   color=colors['original'], linestyle=line_styles['original'],
                   linewidth=2, label='Original Data', alpha=0.8)

        # Plot predicted data
        if pred_col in d.columns:
            pred_mask = d[pred_col].notna()
            if pred_mask.any():
                ax.plot(d.loc[pred_mask, pred_col], d.loc[pred_mask, 'MD'],
                       color=colors['predicted'], linestyle=line_styles['predicted'],
                       linewidth=2, label=f'{model_name} Predicted', alpha=0.8)

        # Calculate and display metrics
        comp = d[[tgt, pred_col]].dropna() if pred_col in d.columns else pd.DataFrame()
        if not comp.empty:
            mae = mean_absolute_error(comp[tgt], comp[pred_col])
            r2 = r2_score(comp[tgt], comp[pred_col])

            title_lines = [
                f"Well: {w}",
                f"MAE: {mae:.3f} | R²: {r2:.3f}"
            ]
            ax.set_title('\n'.join(title_lines), fontsize=10, pad=20)
        else:
            ax.set_title(f"Well: {w}\n(No Comparison Data)", fontsize=10, pad=20)

        # Formatting
        ax.invert_yaxis()
        ax.grid(True, alpha=0.3)
        ax.set_xlabel(f'{tgt} Value', fontsize=10)

    # Set common y-label and legend for Figure 2
    axes2[0].set_ylabel('Measured Depth (MD)', fontsize=12)
    handles2, labels2 = axes2[0].get_legend_handles_labels()
    if handles2:
        fig2.legend(handles2, labels2, loc='upper center', bbox_to_anchor=(0.5, 0.95),
                   ncol=min(len(handles2), 4), fontsize=11, frameon=True,
                   fancybox=True, shadow=True)

    # Title for Figure 2
    fig2.suptitle(f'Original vs Predicted Data - {model_name}\nTarget: {tgt}',
                 fontsize=14, fontweight='bold', y=0.98)

    plt.figure(fig2.number)
    plt.tight_layout()
    plt.subplots_adjust(top=0.85)
    plt.show()

def create_crossplot_analysis(res_df, model_res, cfg, model_name=None, color_by='well'):
    """
    Create cross-plot analysis for quality control with statistical summaries.

    Parameters:
    -----------
    res_df : pandas.DataFrame
        Results dataframe with predictions
    model_res : dict
        Model results dictionary containing evaluations and metadata
    cfg : dict
        Configuration dictionary
    model_name : str, optional
        Name of the model for display in plots
    color_by : str, optional
        Color coding method: 'well', 'depth', or 'none'
    """
    tgt = model_res['target']
    wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()

    # Extract model name for display
    if model_name is None:
        model_name = model_res.get('best_model_name', 'Unknown Model')
        if 'evaluations' in model_res and model_res['evaluations']:
            model_name = model_res['evaluations'][0].get('model_name', model_name)

    # Prepare data for cross-plots
    imputed_col = f"{tgt}_imputed"
    pred_col = f"{tgt}_pred"

    # Create figure with subplots for both comparisons
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # === Cross-plot 1: Original vs Imputed ===
    if imputed_col in res_df.columns:
        comp_imputed = res_df[[tgt, imputed_col, 'WELL', 'MD']].dropna()

        if not comp_imputed.empty:
            # Calculate statistics
            r2_imp = r2_score(comp_imputed[tgt], comp_imputed[imputed_col])
            mae_imp = mean_absolute_error(comp_imputed[tgt], comp_imputed[imputed_col])
            rmse_imp = np.sqrt(mean_squared_error(comp_imputed[tgt], comp_imputed[imputed_col]))
            bias_imp = np.mean(comp_imputed[imputed_col] - comp_imputed[tgt])
            n_imp = len(comp_imputed)

            # Color coding
            if color_by == 'well' and len(wells) > 1:
                colors = plt.cm.Set1(np.linspace(0, 1, len(wells)))
                for i, well in enumerate(wells):
                    well_data = comp_imputed[comp_imputed['WELL'] == well]
                    if not well_data.empty:
                        ax1.scatter(well_data[tgt], well_data[imputed_col],
                                  c=[colors[i]], label=well, alpha=0.6, s=20)
                ax1.legend(title='Wells', bbox_to_anchor=(1.05, 1), loc='upper left')
            elif color_by == 'depth':
                scatter = ax1.scatter(comp_imputed[tgt], comp_imputed[imputed_col],
                                    c=comp_imputed['MD'], cmap='viridis', alpha=0.6, s=20)
                cbar = plt.colorbar(scatter, ax=ax1)
                cbar.set_label('Depth (MD)', rotation=270, labelpad=15)
            else:
                ax1.scatter(comp_imputed[tgt], comp_imputed[imputed_col],
                          alpha=0.6, s=20, color='#A23B72')

            # Add 1:1 reference line
            min_val = min(comp_imputed[tgt].min(), comp_imputed[imputed_col].min())
            max_val = max(comp_imputed[tgt].max(), comp_imputed[imputed_col].max())
            ax1.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2,
                    label='Perfect Prediction (1:1)', alpha=0.8)

            # Statistics text box
            stats_text = f'R² = {r2_imp:.3f}\nMAE = {mae_imp:.3f}\nRMSE = {rmse_imp:.3f}\nBias = {bias_imp:.3f}\nn = {n_imp}'
            ax1.text(0.05, 0.95, stats_text, transform=ax1.transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round',
                    facecolor='lightblue', alpha=0.8), fontsize=10)

            ax1.set_xlabel(f'Original {tgt}', fontsize=12)
            ax1.set_ylabel(f'Imputed {tgt}', fontsize=12)
            ax1.set_title(f'{model_name} - Original vs Imputed\nCross-Plot Analysis', fontsize=12, fontweight='bold')
            ax1.grid(True, alpha=0.3)
            ax1.set_aspect('equal', adjustable='box')

    # === Cross-plot 2: Original vs Predicted ===
    if pred_col in res_df.columns:
        comp_pred = res_df[[tgt, pred_col, 'WELL', 'MD']].dropna()

        if not comp_pred.empty:
            # Calculate statistics
            r2_pred = r2_score(comp_pred[tgt], comp_pred[pred_col])
            mae_pred = mean_absolute_error(comp_pred[tgt], comp_pred[pred_col])
            rmse_pred = np.sqrt(mean_squared_error(comp_pred[tgt], comp_pred[pred_col]))
            bias_pred = np.mean(comp_pred[pred_col] - comp_pred[tgt])
            n_pred = len(comp_pred)

            # Color coding
            if color_by == 'well' and len(wells) > 1:
                colors = plt.cm.Set1(np.linspace(0, 1, len(wells)))
                for i, well in enumerate(wells):
                    well_data = comp_pred[comp_pred['WELL'] == well]
                    if not well_data.empty:
                        ax2.scatter(well_data[tgt], well_data[pred_col],
                                  c=[colors[i]], label=well, alpha=0.6, s=20)
                ax2.legend(title='Wells', bbox_to_anchor=(1.05, 1), loc='upper left')
            elif color_by == 'depth':
                scatter = ax2.scatter(comp_pred[tgt], comp_pred[pred_col],
                                    c=comp_pred['MD'], cmap='viridis', alpha=0.6, s=20)
                cbar = plt.colorbar(scatter, ax=ax2)
                cbar.set_label('Depth (MD)', rotation=270, labelpad=15)
            else:
                ax2.scatter(comp_pred[tgt], comp_pred[pred_col],
                          alpha=0.6, s=20, color='#F18F01')

            # Add 1:1 reference line
            min_val = min(comp_pred[tgt].min(), comp_pred[pred_col].min())
            max_val = max(comp_pred[tgt].max(), comp_pred[pred_col].max())
            ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2,
                    label='Perfect Prediction (1:1)', alpha=0.8)

            # Statistics text box
            stats_text = f'R² = {r2_pred:.3f}\nMAE = {mae_pred:.3f}\nRMSE = {rmse_pred:.3f}\nBias = {bias_pred:.3f}\nn = {n_pred}'
            ax2.text(0.05, 0.95, stats_text, transform=ax2.transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round',
                    facecolor='lightyellow', alpha=0.8), fontsize=10)

            ax2.set_xlabel(f'Original {tgt}', fontsize=12)
            ax2.set_ylabel(f'Predicted {tgt}', fontsize=12)
            ax2.set_title(f'{model_name} - Original vs Predicted\nCross-Plot Analysis', fontsize=12, fontweight='bold')
            ax2.grid(True, alpha=0.3)
            ax2.set_aspect('equal', adjustable='box')

    # Overall title
    fig.suptitle(f'Cross-Plot Quality Control Analysis - {model_name}\nTarget: {tgt}',
                fontsize=16, fontweight='bold', y=0.98)

    plt.tight_layout()
    plt.subplots_adjust(top=0.88)
    plt.show()

def create_model_ranking_visualization(all_results, target_log, combined_evaluations):
    """
    Create comprehensive model ranking visualization with multiple performance metrics.

    Parameters:
    -----------
    all_results : dict
        Dictionary with model_key -> {'res_df': df, 'mres': results, 'model_config': config}
    target_log : str
        Target log name
    combined_evaluations : list
        List of evaluation dictionaries from all models
    """
    if not combined_evaluations:
        print("No evaluation data available for ranking.")
        return

    # Prepare ranking data
    ranking_data = []
    for eval_result in combined_evaluations:
        model_key = eval_result.get('model_key', eval_result.get('model_name', 'Unknown'))
        model_name = eval_result.get('model_name', model_key)

        # Calculate additional metrics if available
        model_data = {
            'model_key': model_key,
            'model_name': model_name,
            'r2': eval_result.get('r2', 0),
            'mae': eval_result.get('mae', float('inf')),
            'rmse': eval_result.get('rmse', float('inf')),
            'composite_score': eval_result.get('composite_score', float('inf'))
        }

        # Calculate consistency across wells if multiple wells exist
        if model_key in all_results:
            res_df = all_results[model_key]['res_df']
            wells = res_df['WELL'].unique()

            if len(wells) > 1:
                well_r2_scores = []
                imputed_col = f"{target_log}_imputed"

                for well in wells:
                    well_data = res_df[res_df['WELL'] == well]
                    comp = well_data[[target_log, imputed_col]].dropna()
                    if len(comp) > 1:
                        well_r2 = r2_score(comp[target_log], comp[imputed_col])
                        well_r2_scores.append(well_r2)

                if well_r2_scores:
                    model_data['consistency'] = 1 - np.std(well_r2_scores)  # Higher is better
                    model_data['min_well_r2'] = min(well_r2_scores)
                    model_data['max_well_r2'] = max(well_r2_scores)
                else:
                    model_data['consistency'] = 0
                    model_data['min_well_r2'] = 0
                    model_data['max_well_r2'] = 0
            else:
                model_data['consistency'] = 1  # Perfect consistency for single well
                model_data['min_well_r2'] = model_data['r2']
                model_data['max_well_r2'] = model_data['r2']

        ranking_data.append(model_data)

    # Convert to DataFrame for easier manipulation
    df_ranking = pd.DataFrame(ranking_data)

    # Sort by composite score (lower is better)
    df_ranking = df_ranking.sort_values('composite_score')

    # Create visualization
    fig = plt.figure(figsize=(16, 12))

    # === Subplot 1: Overall Performance Bar Chart ===
    ax1 = plt.subplot(2, 2, 1)

    # Normalize scores for visualization (invert for MAE, RMSE, composite_score)
    df_viz = df_ranking.copy()
    df_viz['r2_norm'] = df_viz['r2']  # Higher is better
    df_viz['mae_norm'] = 1 / (1 + df_viz['mae'])  # Convert to "higher is better"
    df_viz['composite_norm'] = 1 / (1 + df_viz['composite_score'])  # Convert to "higher is better"

    x_pos = np.arange(len(df_viz))
    width = 0.25

    bars1 = ax1.bar(x_pos - width, df_viz['r2_norm'], width, label='R² Score', color='#2E86AB', alpha=0.8)
    bars2 = ax1.bar(x_pos, df_viz['mae_norm'], width, label='MAE (inverted)', color='#A23B72', alpha=0.8)
    bars3 = ax1.bar(x_pos + width, df_viz['composite_norm'], width, label='Composite (inverted)', color='#F18F01', alpha=0.8)

    ax1.set_xlabel('Models')
    ax1.set_ylabel('Normalized Performance Score')
    ax1.set_title('Model Performance Comparison\n(Higher bars = Better performance)')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(df_viz['model_name'], rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3, axis='y')

    # Add value labels on bars
    for bars in [bars1, bars2, bars3]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=8)

    # === Subplot 2: R² Score Ranking ===
    ax2 = plt.subplot(2, 2, 2)

    colors = ['#FFD700', '#C0C0C0', '#CD7F32'] + ['#87CEEB'] * (len(df_viz) - 3)  # Gold, Silver, Bronze, Light blue
    bars = ax2.barh(range(len(df_viz)), df_viz['r2'], color=colors[:len(df_viz)])

    ax2.set_yticks(range(len(df_viz)))
    ax2.set_yticklabels(df_viz['model_name'])
    ax2.set_xlabel('R² Score')
    ax2.set_title('Model Ranking by R² Score')
    ax2.grid(True, alpha=0.3, axis='x')

    # Add value labels
    for i, (bar, r2) in enumerate(zip(bars, df_viz['r2'])):
        ax2.text(r2 + 0.01, bar.get_y() + bar.get_height()/2,
                f'{r2:.3f}', va='center', fontsize=10)

        # Add rank medals using display utility
        if i == 0:
            add_ranking_annotation(ax2, 1, -0.05, bar.get_y() + bar.get_height()/2,
                                 va='center', ha='right', fontsize=12)
        elif i == 1:
            add_ranking_annotation(ax2, 2, -0.05, bar.get_y() + bar.get_height()/2,
                                 va='center', ha='right', fontsize=12)
        elif i == 2:
            add_ranking_annotation(ax2, 3, -0.05, bar.get_y() + bar.get_height()/2,
                                 va='center', ha='right', fontsize=12)

    # === Subplot 3: Consistency Analysis (if multiple wells) ===
    ax3 = plt.subplot(2, 2, 3)

    if 'consistency' in df_viz.columns and df_viz['consistency'].notna().any():
        scatter = ax3.scatter(df_viz['r2'], df_viz['consistency'],
                            s=100, alpha=0.7, c=range(len(df_viz)), cmap='viridis')

        # Add model labels
        for i, row in df_viz.iterrows():
            ax3.annotate(row['model_name'], (row['r2'], row['consistency']),
                        xytext=(5, 5), textcoords='offset points', fontsize=9)

        ax3.set_xlabel('R² Score')
        ax3.set_ylabel('Consistency Across Wells')
        ax3.set_title('Performance vs Consistency Analysis')
        ax3.grid(True, alpha=0.3)

        # Add quadrant labels
        ax3.axhline(y=0.8, color='gray', linestyle='--', alpha=0.5)
        ax3.axvline(x=0.8, color='gray', linestyle='--', alpha=0.5)
        ax3.text(0.95, 0.95, 'High Performance\nHigh Consistency',
                transform=ax3.transAxes, ha='right', va='top',
                bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))
    else:
        ax3.text(0.5, 0.5, 'Consistency Analysis\nNot Available\n(Single well or insufficient data)',
                transform=ax3.transAxes, ha='center', va='center', fontsize=12,
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.7))
        ax3.set_xlim(0, 1)
        ax3.set_ylim(0, 1)

    # === Subplot 4: Performance Summary Table ===
    ax4 = plt.subplot(2, 2, 4)
    ax4.axis('off')

    # Create summary table
    table_data = []
    for i, row in df_viz.iterrows():
        rank_symbol = get_ranking_symbol(i + 1)
        table_data.append([
            rank_symbol,
            row['model_name'],
            f"{row['r2']:.3f}",
            f"{row['mae']:.3f}",
            f"{row.get('consistency', 0):.3f}" if 'consistency' in row else "N/A"
        ])

    table = ax4.table(cellText=table_data,
                     colLabels=['Rank', 'Model', 'R²', 'MAE', 'Consistency'],
                     cellLoc='center',
                     loc='center',
                     bbox=[0, 0, 1, 1])

    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)

    # Color code the table rows
    for i in range(len(table_data)):
        if i == 0:  # Best model
            for j in range(5):
                table[(i+1, j)].set_facecolor('#FFD700')  # Gold
        elif i == 1:  # Second best
            for j in range(5):
                table[(i+1, j)].set_facecolor('#C0C0C0')  # Silver
        elif i == 2:  # Third best
            for j in range(5):
                table[(i+1, j)].set_facecolor('#CD7F32')  # Bronze

    ax4.set_title('Performance Summary Table', fontsize=12, fontweight='bold', pad=20)

    # Overall title
    fig.suptitle(f'Comprehensive Model Ranking Analysis\nTarget: {target_log}',
                fontsize=16, fontweight='bold', y=0.98)

    plt.tight_layout()
    plt.subplots_adjust(top=0.92)
    plt.show()

    # Print ranking summary using display utility
    rankings_data = []
    for i, row in df_viz.iterrows():
        rankings_data.append({
            'rank': i + 1,
            'model_name': row['model_name'],
            'r2': row['r2'],
            'mae': row['mae'],
            'composite_score': row['composite_score']
        })
    
    print_ranking_summary(rankings_data, "MODEL RANKING SUMMARY")

def create_side_by_side_prediction_comparison(all_results, cfg, target_log, wells):
    """
    Create side-by-side prediction comparison showing all models' predictions for the same data.

    Parameters:
    -----------
    all_results : dict
        Dictionary with model_key -> {'res_df': df, 'mres': results, 'model_config': config}
    cfg : dict
        Configuration dictionary
    target_log : str
        Target log name
    wells : list
        List of wells to compare
    """
    model_keys = list(all_results.keys())
    n_models = len(model_keys)
    n_wells = min(len(wells), 3)  # Limit to 3 wells for readability

    # Create figure with subplots for each well
    fig, axes = plt.subplots(n_wells, 1, figsize=(16, 6*n_wells))
    if n_wells == 1:
        axes = [axes]

    # Define colors for each model
    colors = plt.cm.Set1(np.linspace(0, 1, n_models))

    for i, well in enumerate(wells[:n_wells]):
        ax = axes[i]

        # Get data for this well from first model (for original data and depth)
        first_model_data = list(all_results.values())[0]
        well_data = first_model_data['res_df'][first_model_data['res_df']['WELL'] == well].copy()

        if well_data.empty:
            continue

        # Sort by depth for proper plotting
        well_data = well_data.sort_values('MD')

        # Plot original data
        original_mask = well_data[target_log].notna()
        ax.plot(well_data.loc[original_mask, 'MD'], well_data.loc[original_mask, target_log],
                'ko-', markersize=3, linewidth=1, label='Original Data', alpha=0.7)

        # Plot predictions from each model
        for j, model_key in enumerate(model_keys):
            model_data = all_results[model_key]
            model_well_data = model_data['res_df'][model_data['res_df']['WELL'] == well].copy()

            if model_well_data.empty:
                continue

            model_well_data = model_well_data.sort_values('MD')

            # Get model performance metrics
            mres = model_data['mres']
            if 'evaluations' in mres and mres['evaluations']:
                eval_data = mres['evaluations'][0]
                mae = eval_data.get('mae', 0)
                r2 = eval_data.get('r2', 0)
                model_label = f"{model_key} (MAE: {mae:.2f}, R²: {r2:.3f})"
            else:
                model_label = model_key

            # Plot predictions
            pred_col = f"{target_log}_pred"
            if pred_col in model_well_data.columns:
                pred_mask = model_well_data[pred_col].notna()
                ax.plot(model_well_data.loc[pred_mask, 'MD'], model_well_data.loc[pred_mask, pred_col],
                       color=colors[j], linewidth=2, label=model_label, alpha=0.8)

        ax.set_xlabel('Measured Depth (MD)')
        ax.set_ylabel(target_log)
        ax.set_title(f'Model Predictions Comparison - Well: {well}')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        ax.invert_yaxis()  # Typical for well logs

    plt.suptitle(f'Side-by-Side Model Predictions - Target: {target_log}',
                fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.subplots_adjust(top=0.94)
    plt.show()

def create_multi_model_comparison_plots(all_results, cfg, target_log):
    """
    Create comprehensive comparison plots for multiple models.

    Parameters:
    -----------
    all_results : dict
        Dictionary with model_key -> {'res_df': df, 'mres': results, 'model_config': config}
    cfg : dict
        Configuration dictionary
    target_log : str
        Target log name
    """
    if not all_results:
        print("No results to compare.")
        return

    model_keys = list(all_results.keys())
    wells = None

    # Get wells from first available result
    for model_key in model_keys:
        res_df = all_results[model_key]['res_df']
        wells = cfg['prediction_wells'] if cfg['mode']=='separated' else res_df['WELL'].unique()
        wells = wells[:4]  # Limit for comparison readability
        break

    if wells is None:
        print("No wells found for comparison.")
        return

    # Create side-by-side prediction comparison
    create_side_by_side_prediction_comparison(all_results, cfg, target_log, wells)

    # Create traditional multi-model comparison figure
    n_models = len(model_keys)
    n_wells = len(wells)

    fig, axes = plt.subplots(n_wells, n_models, figsize=(4*n_models, 6*n_wells),
                            sharex='col', sharey='row')

    # Handle single well or single model cases
    if n_wells == 1 and n_models == 1:
        axes = [[axes]]
    elif n_wells == 1:
        axes = [axes]
    elif n_models == 1:
        axes = [[ax] for ax in axes]

    # Define colors for each model
    model_colors = plt.cm.Set1(np.linspace(0, 1, n_models))

    # Plot each model's results
    for j, model_key in enumerate(model_keys):
        model_data = all_results[model_key]
        res_df = model_data['res_df']
        mres = model_data['mres']
        model_name = mres.get('best_model_name', model_key)

        for i, well in enumerate(wells):
            ax = axes[i][j]
            d = res_df[res_df['WELL'] == well].copy()

            if d.empty:
                ax.set_title(f"{well}\n{model_name}\n(No Data)")
                continue

            # Plot original data
            original_mask = d[target_log].notna()
            if original_mask.any():
                ax.plot(d.loc[original_mask, target_log], d.loc[original_mask, 'MD'],
                       'k-', linewidth=2, label='Original', alpha=0.8)

            # Plot imputed data
            imputed_col = f"{target_log}_imputed"
            if imputed_col in d.columns:
                imputed_mask = d[imputed_col].notna()
                if imputed_mask.any():
                    ax.plot(d.loc[imputed_mask, imputed_col], d.loc[imputed_mask, 'MD'],
                           color=model_colors[j], linestyle='--', linewidth=2,
                           label=f'{model_name} Imputed', alpha=0.8)

            # Calculate metrics
            comp = d[[target_log, imputed_col]].dropna() if imputed_col in d.columns else pd.DataFrame()
            if not comp.empty:
                mae = mean_absolute_error(comp[target_log], comp[imputed_col])
                r2 = r2_score(comp[target_log], comp[imputed_col])
                title = f"{well}\n{model_name}\nMAE: {mae:.3f} | R²: {r2:.3f}"
            else:
                title = f"{well}\n{model_name}\n(No Comparison)"

            ax.set_title(title, fontsize=10)
            ax.invert_yaxis()
            ax.grid(True, alpha=0.3)

            # Add legend to first subplot of each row
            if j == 0:
                ax.legend(loc='upper right', fontsize=8)

            # Labels
            if i == n_wells - 1:  # Bottom row
                ax.set_xlabel(f'{target_log} Value')
            if j == 0:  # First column
                ax.set_ylabel('Measured Depth (MD)')

    # Overall title
    fig.suptitle(f'Multi-Model Comparison - Target: {target_log}',
                fontsize=16, fontweight='bold', y=0.98)

    plt.tight_layout()
    plt.subplots_adjust(top=0.94)
    plt.show()

def generate_final_report(model_res, hparams=None):
    """
    Generate enhanced final report with model performance summary.

    Parameters:
    -----------
    model_res : dict
        Model results dictionary
    hparams : dict, optional
        Hyperparameters dictionary (for backward compatibility)
    """
    print("\n" + "="*60)
    print(" FINAL MODEL PERFORMANCE REPORT")
    print("="*60)

    print(f"\n🎯 Target Log: {model_res['target']}")

    if 'evaluations' in model_res and model_res['evaluations']:
        print(f"\n📊 Model Performance Summary:")
        print("-" * 50)
        print(f"{'Rank':<6} {'Model':<20} {'MAE':<10} {'R²':<10} {'Score':<10}")
        print("-" * 50)

        # Sort by composite score (lower is better)
        sorted_evals = sorted(model_res['evaluations'],
                            key=lambda x: x.get('composite_score', float('inf')))

        for i, eval_result in enumerate(sorted_evals, 1):
            model_name = eval_result.get('model_name', 'Unknown')
            mae = eval_result.get('mae', 0)
            r2 = eval_result.get('r2', 0)
            score = eval_result.get('composite_score', 0)

            # Add medal symbol for top 3 using display utility
            rank_symbol = get_ranking_symbol(i)

            print(f"{rank_symbol:<6} {model_name:<20} {mae:<10.3f} {r2:<10.3f} {score:<10.3f}")

        print("-" * 50)

        # Highlight best model
        best_model = sorted_evals[0]
        print(f"\n🏆 Best Performing Model: {best_model.get('model_name', 'Unknown')}")
        print(f"   • Mean Absolute Error: {best_model.get('mae', 0):.3f}")
        print(f"   • R-squared Score: {best_model.get('r2', 0):.3f}")
        print(f"   • Composite Score: {best_model.get('composite_score', 0):.3f}")

        # Performance interpretation
        r2_best = best_model.get('r2', 0)
        if r2_best >= 0.9:
            performance = "Excellent (R² ≥ 0.9)"
        elif r2_best >= 0.8:
            performance = "Very Good (R² ≥ 0.8)"
        elif r2_best >= 0.7:
            performance = "Good (R² ≥ 0.7)"
        elif r2_best >= 0.5:
            performance = "Fair (R² ≥ 0.5)"
        else:
            performance = "Poor (R² < 0.5)"

        print(f"   • Performance Rating: {performance}")

        # Display mathematical equation for MLR models
        if 'mathematical_equation' in best_model:
            equation_info = best_model['mathematical_equation']
            print(f"\n📐 Mathematical Equation ({equation_info['model_type'].replace('_', ' ').title()}):")
            print(f"   {equation_info['equation_text']}")

            print(f"\n📋 Feature Interpretation:")
            for feature, coef in equation_info['coefficients'].items():
                correlation = "positive" if coef > 0 else "negative"
                print(f"   • {feature}: {coef:+.3f} ({correlation} correlation)")

            print(f"   • Intercept: {equation_info['intercept']:+.3f}")

            print(f"\n💡 Interpretability Note:")
            print(f"   {equation_info['interpretability_note']}")

    else:
        print("\n⚠️ No evaluation results available.")

    print("\n" + "="*60)

def generate_mathematical_equation_report(model_res, target_log, output_file=None):
    """
    Generate a dedicated mathematical equation report for MLR models.

    Parameters:
    -----------
    model_res : dict
        Model results dictionary containing evaluations
    target_log : str
        Name of the target log
    output_file : str, optional
        Path to save the report file
    """
    # Find MLR models with equations
    mlr_equations = []

    if 'evaluations' in model_res and model_res['evaluations']:
        for eval_result in model_res['evaluations']:
            if 'mathematical_equation' in eval_result:
                mlr_equations.append(eval_result)

    if not mlr_equations:
        print("📐 No mathematical equations found in model results.")
        return

    # Generate report content
    report_lines = [
        "=" * 80,
        " MATHEMATICAL EQUATION REPORT",
        " Multiple Linear Regression Models for Well Log Imputation",
        "=" * 80,
        "",
        f"Target Log: {target_log}",
        f"Generated: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "INTERPRETABILITY RANKING:",
        "1. Linear Regression - Most interpretable (no regularization bias)",
        "2. Ridge Regression - Good interpretability (L2 regularization)",
        "3. Lasso Regression - Moderate interpretability (L1 feature selection)",
        "4. ElasticNet - Complex interpretability (combined regularization)",
        "",
        "=" * 80
    ]

    # Sort by interpretability (Linear first, then by performance)
    interpretability_order = {'linear': 1, 'ridge': 2, 'lasso': 3, 'elastic_net': 4}

    sorted_equations = sorted(mlr_equations,
                            key=lambda x: (interpretability_order.get(x['mathematical_equation']['model_type'], 5),
                                         x.get('composite_score', float('inf'))))

    for i, eval_result in enumerate(sorted_equations, 1):
        equation_info = eval_result['mathematical_equation']
        model_name = eval_result.get('model_name', 'Unknown')

        report_lines.extend([
            "",
            f"{i}. {model_name.upper()}",
            "-" * 60,
            "",
            "📐 Mathematical Equation:",
            f"   {equation_info['equation_text']}",
            "",
            "📊 Model Performance:",
            f"   • R² Score: {eval_result.get('r2', 0):.4f}",
            f"   • Mean Absolute Error: {eval_result.get('mae', 0):.3f}",
            f"   • Root Mean Square Error: {eval_result.get('rmse', 0):.3f}",
            "",
            "📋 Coefficient Analysis:",
        ])

        # Add coefficient interpretations
        for feature, coef in equation_info['coefficients'].items():
            correlation = "positive" if coef > 0 else "negative"
            magnitude = "strong" if abs(coef) > 10 else "moderate" if abs(coef) > 1 else "weak"
            report_lines.append(f"   • {feature:<8}: {coef:+8.3f} ({magnitude} {correlation} correlation)")

        report_lines.extend([
            f"   • Intercept: {equation_info['intercept']:+8.3f}",
            "",
            "💡 Interpretability:",
            f"   {equation_info['interpretability_note']}",
            "",
            "🌍 Geological Interpretation:",
        ])

        # Add geological context
        geological_context = {
            'GR': 'Gamma Ray indicates clay content and lithology',
            'NPHI': 'Neutron Porosity measures formation porosity',
            'RHOB': 'Bulk Density indicates formation density and porosity',
            'DT': 'Delta Time (Sonic) relates to porosity and lithology',
            'RT': 'Resistivity indicates hydrocarbon saturation',
            'SP': 'Spontaneous Potential indicates permeability and lithology',
            'CALI': 'Caliper measures borehole diameter',
            'MD': 'Measured Depth shows depth trend effects'
        }

        for feature, coef in equation_info['coefficients'].items():
            if feature in geological_context:
                context = geological_context[feature]
                trend = "increases" if coef > 0 else "decreases"
                report_lines.extend([
                    f"   • {feature}: {context}",
                    f"     → Target {trend} by {abs(coef):.3f} units per unit increase in {feature}"
                ])

        if i < len(sorted_equations):
            report_lines.append("")

    report_lines.extend([
        "",
        "=" * 80,
        " USAGE RECOMMENDATIONS",
        "=" * 80,
        "",
        "🎯 For Maximum Interpretability:",
        "   Use Linear Regression - provides true, unbiased relationships",
        "",
        "🔧 For Multicollinear Data:",
        "   Use Ridge Regression - handles correlated features while maintaining interpretability",
        "",
        "✂️ For Feature Selection:",
        "   Use Lasso Regression - automatically selects most important features",
        "",
        "⚖️ For Balanced Approach:",
        "   Use ElasticNet - combines regularization and feature selection",
        "",
        "📈 Model Selection Criteria:",
        "   • R² > 0.8: Excellent predictive performance",
        "   • R² 0.6-0.8: Good predictive performance",
        "   • R² < 0.6: Consider non-linear models or feature engineering",
        "",
        "=" * 80
    ])

    # Print to console
    print("\n".join(report_lines))

    # Save to file if requested
    if output_file:
        try:
            with open(output_file, 'w') as f:
                f.write('\n'.join(report_lines))
            print(f"\n📄 Mathematical equation report saved to: {output_file}")
        except Exception as e:
            print(f"⚠️ Failed to save report to {output_file}: {e}")

    return '\n'.join(report_lines)
