#!/usr/bin/env python3
"""
Test script to validate mRNN training optimizations and performance improvements.

This script compares the optimized mRNN configuration against the original
to ensure 50%+ training time reduction while maintaining accuracy.
"""

import torch
import numpy as np
import time
from typing import Dict, Any, <PERSON>ple
import warnings
warnings.filterwarnings('ignore')

def create_synthetic_well_log_data(n_samples: int = 10000, sequence_len: int = 64, 
                                  n_features: int = 4, missing_rate: float = 0.3) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
    """
    Create synthetic well log data for testing.
    
    Args:
        n_samples: Number of samples
        sequence_len: Length of each sequence
        n_features: Number of features (GR, NPHI, RHOB, target)
        missing_rate: Proportion of missing values
        
    Returns:
        Tuple of (data_with_missing, complete_data)
    """
    print(f"🔧 Creating synthetic well log data...")
    print(f"   Samples: {n_samples:,}")
    print(f"   Sequence length: {sequence_len}")
    print(f"   Features: {n_features}")
    print(f"   Missing rate: {missing_rate:.1%}")
    
    # Generate realistic well log patterns
    np.random.seed(42)
    torch.manual_seed(42)
    
    # Create depth-based trends
    depth = np.linspace(0, 1000, sequence_len)
    
    complete_data = []
    for i in range(n_samples):
        sample = np.zeros((sequence_len, n_features))
        
        # GR (Gamma Ray) - typically 0-200 API
        gr_trend = 50 + 30 * np.sin(depth / 100) + np.random.normal(0, 10, sequence_len)
        sample[:, 0] = np.clip(gr_trend, 0, 200)
        
        # NPHI (Neutron Porosity) - typically 0-0.4
        nphi_trend = 0.15 + 0.1 * np.cos(depth / 150) + np.random.normal(0, 0.02, sequence_len)
        sample[:, 1] = np.clip(nphi_trend, 0, 0.4)
        
        # RHOB (Bulk Density) - typically 1.5-3.0 g/cc
        rhob_trend = 2.3 - 0.3 * sample[:, 1] + np.random.normal(0, 0.05, sequence_len)
        sample[:, 2] = np.clip(rhob_trend, 1.5, 3.0)
        
        # Target log (e.g., resistivity) - correlated with other logs
        target_trend = 10 * np.exp(sample[:, 0] / 100) * (1 - sample[:, 1]) + np.random.normal(0, 5, sequence_len)
        sample[:, 3] = np.clip(target_trend, 0.1, 1000)
        
        complete_data.append(sample)
    
    complete_data = np.array(complete_data)
    complete_tensor = torch.FloatTensor(complete_data)
    
    # Create missing values
    missing_tensor = complete_tensor.clone()
    missing_mask = torch.rand_like(missing_tensor) < missing_rate
    missing_tensor[missing_mask] = float('nan')
    
    print(f"✅ Synthetic data created")
    print(f"   Total missing values: {torch.isnan(missing_tensor).sum().item():,}")
    
    return missing_tensor, complete_tensor


def test_mrnn_performance(config_name: str, model_params: Dict[str, Any], 
                         train_data: torch.Tensor, truth_data: torch.Tensor) -> Dict[str, Any]:
    """
    Test mRNN model performance with given configuration.
    
    Args:
        config_name: Name of the configuration
        model_params: Model parameters
        train_data: Training data with missing values
        truth_data: Complete ground truth data
        
    Returns:
        Performance metrics
    """
    print(f"\n🧪 Testing {config_name} configuration...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Create model with specified parameters
        model = MRNNModel(**model_params)
        
        # Record training start time
        start_time = time.time()
        
        # Train the model
        model.fit(train_data, truth_data)
        
        # Record training end time
        end_time = time.time()
        training_time = end_time - start_time
        
        # Get training history
        history = model.training_history
        
        # Calculate performance metrics
        final_loss = history['loss'][-1] if history['loss'] else float('inf')
        final_val_loss = history['val_loss'][-1] if history['val_loss'] else float('inf')
        epochs_completed = len(history['loss'])
        
        results = {
            'config_name': config_name,
            'training_time': training_time,
            'epochs_completed': epochs_completed,
            'final_loss': final_loss,
            'final_val_loss': final_val_loss,
            'converged': final_val_loss < 1.0,  # Reasonable convergence threshold
            'parameters': sum(p.numel() for p in model.model.parameters()) if hasattr(model, 'model') else 0
        }
        
        print(f"✅ {config_name} completed:")
        print(f"   Training time: {training_time:.1f}s")
        print(f"   Epochs: {epochs_completed}")
        print(f"   Final loss: {final_loss:.6f}")
        print(f"   Final val loss: {final_val_loss:.6f}")
        
        return results
        
    except Exception as e:
        print(f"❌ {config_name} failed: {e}")
        return {
            'config_name': config_name,
            'training_time': float('inf'),
            'epochs_completed': 0,
            'final_loss': float('inf'),
            'final_val_loss': float('inf'),
            'converged': False,
            'error': str(e)
        }


def main():
    """Run mRNN optimization performance test."""
    print("=" * 80)
    print("🚀 mRNN TRAINING OPTIMIZATION PERFORMANCE TEST")
    print("=" * 80)
    
    # Check CUDA availability
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  Device: {device}")
    if torch.cuda.is_available():
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        print(f"   Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Create test data
    train_data, truth_data = create_synthetic_well_log_data(
        n_samples=5000,  # Smaller dataset for faster testing
        sequence_len=64,
        n_features=4,
        missing_rate=0.3
    )
    
    # Original configuration (baseline)
    original_config = {
        'n_features': 4,
        'sequence_len': 64,
        'hidden_sizes': [64, 128, 256],
        'n_layers': 3,
        'epochs': 75,  # Original high epoch count
        'batch_size': 32,  # Original small batch size
        'learning_rate': 1e-3,
        'use_onecycle_lr': False,  # Original scheduler
        'enable_model_compile': False,  # Original without compilation
        'num_workers': 0,  # Original without workers
        'verbose': False  # Reduce output for testing
    }
    
    # Optimized configuration
    optimized_config = {
        'n_features': 4,
        'sequence_len': 64,
        'hidden_sizes': [64, 128, 256],
        'n_layers': 3,
        'epochs': 35,  # Reduced epochs
        'batch_size': 96,  # Increased batch size
        'learning_rate': 1e-3,
        'use_onecycle_lr': True,  # Advanced scheduler
        'enable_model_compile': True,  # Model compilation
        'num_workers': 4,  # Optimized workers (reduced for testing)
        'verbose': False  # Reduce output for testing
    }
    
    # Run tests
    print(f"\n📊 Running performance comparison...")
    
    # Test original configuration
    original_results = test_mrnn_performance("Original", original_config, train_data, truth_data)
    
    # Test optimized configuration
    optimized_results = test_mrnn_performance("Optimized", optimized_config, train_data, truth_data)
    
    # Compare results
    print(f"\n" + "=" * 80)
    print("📈 PERFORMANCE COMPARISON RESULTS")
    print("=" * 80)
    
    if original_results['training_time'] != float('inf') and optimized_results['training_time'] != float('inf'):
        time_reduction = (original_results['training_time'] - optimized_results['training_time']) / original_results['training_time']
        speedup = original_results['training_time'] / optimized_results['training_time']
        
        print(f"⏱️  Training Time:")
        print(f"   Original:  {original_results['training_time']:.1f}s")
        print(f"   Optimized: {optimized_results['training_time']:.1f}s")
        print(f"   Reduction: {time_reduction:.1%}")
        print(f"   Speedup:   {speedup:.1f}x")
        
        print(f"\n🎯 Epochs Completed:")
        print(f"   Original:  {original_results['epochs_completed']}")
        print(f"   Optimized: {optimized_results['epochs_completed']}")
        
        print(f"\n📉 Final Validation Loss:")
        print(f"   Original:  {original_results['final_val_loss']:.6f}")
        print(f"   Optimized: {optimized_results['final_val_loss']:.6f}")
        
        print(f"\n✅ Convergence:")
        print(f"   Original:  {'✓' if original_results['converged'] else '✗'}")
        print(f"   Optimized: {'✓' if optimized_results['converged'] else '✗'}")
        
        # Success criteria
        target_reduction = 0.5  # 50% reduction target
        success = time_reduction >= target_reduction and optimized_results['converged']
        
        print(f"\n🏆 OPTIMIZATION SUCCESS: {'✅ YES' if success else '❌ NO'}")
        if success:
            print(f"   ✓ Achieved {time_reduction:.1%} training time reduction (target: {target_reduction:.1%})")
            print(f"   ✓ Model converged successfully")
        else:
            if time_reduction < target_reduction:
                print(f"   ✗ Only achieved {time_reduction:.1%} reduction (target: {target_reduction:.1%})")
            if not optimized_results['converged']:
                print(f"   ✗ Model did not converge properly")
    
    else:
        print("❌ One or both configurations failed to complete")
        if 'error' in original_results:
            print(f"   Original error: {original_results['error']}")
        if 'error' in optimized_results:
            print(f"   Optimized error: {optimized_results['error']}")
    
    print(f"\n" + "=" * 80)
    print("🎉 Performance test completed!")
    print("=" * 80)


if __name__ == '__main__':
    main()
