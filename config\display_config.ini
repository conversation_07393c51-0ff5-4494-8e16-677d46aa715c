# Display Configuration for ML Log Prediction System
# This file controls emoji and font display settings

[display]
# Whether to prefer emoji symbols over text alternatives
prefer_emoji = true

# Fallback mode when emojis aren't available
# Options: 'text', 'unicode', 'simple'
fallback_mode = text

# Whether to suppress matplotlib font warnings
suppress_warnings = true

# Whether to automatically configure matplotlib fonts
auto_configure_fonts = true

[fonts]
# Custom font preferences (optional)
# Leave empty to use system defaults
# Examples:
# windows_fonts = Segoe UI Emoji, Microsoft YaHei, Arial Unicode MS
# macos_fonts = Apple Color Emoji, Helvetica Neue, Arial Unicode MS  
# linux_fonts = Noto Color Emoji, Noto Emoji, DejaVu Sans

[symbols]
# Custom symbol preferences for different modes
# You can override the default symbols here

# Emoji mode symbols (requires emoji support)
emoji_first = 🥇
emoji_second = 🥈
emoji_third = 🥉

# Text mode symbols (always available)
text_first = [1st]
text_second = [2nd]
text_third = [3rd]

# Unicode mode symbols (requires Unicode support)
unicode_first = ①
unicode_second = ②
unicode_third = ③

[performance]
# Performance-related display settings

# Maximum number of models to show in rankings
max_ranking_display = 10

# Whether to show detailed metrics in console output
show_detailed_metrics = true

# Whether to include performance interpretation
show_performance_interpretation = true

[compatibility]
# Cross-platform compatibility settings

# Force text mode on specific systems (comma-separated)
# force_text_mode_systems = 

# Disable emoji support entirely
disable_emoji = false

# Use safe fonts only (no emoji fonts)
safe_fonts_only = false