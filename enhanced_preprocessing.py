"""
Enhanced preprocessing module for ML log prediction.
Integrates advanced techniques from cp_preconditioning with deep learning optimizations.
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, RobustScaler
from typing import Dict, List, Tuple, Optional
import warnings

class EnhancedLogPreprocessor:
    """
    Enhanced preprocessing class that combines cp_preconditioning techniques
    with deep learning best practices for well log data.
    """
    
    def __init__(self, 
                 winsorize_percentiles: Tuple[float, float] = (0.01, 0.99),
                 normalization_method: str = 'standard',
                 sequence_len: int = 64,
                 sequence_stride: int = 32,
                 missing_rate: float = 0.2,
                 random_seed: int = 42):
        """
        Initialize the enhanced preprocessor.
        
        Args:
            winsorize_percentiles: Percentiles for outlier clipping (lower, upper)
            normalization_method: 'standard', 'robust', or 'minmax'
            sequence_len: Length of sequences for deep learning models
            sequence_stride: Stride between sequence start points
            missing_rate: Rate of missing values to introduce for training
            random_seed: Random seed for reproducibility
        """
        self.winsorize_percentiles = winsorize_percentiles
        self.normalization_method = normalization_method
        self.sequence_len = sequence_len
        self.sequence_stride = sequence_stride
        self.missing_rate = missing_rate
        self.random_seed = random_seed
        
        # Storage for preprocessing parameters
        self.scalers = {}
        self.outlier_bounds = {}
        self.preprocessing_stats = {}
        
        np.random.seed(random_seed)
    
    def winsorize_outliers(self, df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """
        Apply statistical winsorization to remove outliers.
        More robust than hard-coded thresholds.
        
        Args:
            df: Input dataframe
            columns: Columns to winsorize
            
        Returns:
            DataFrame with outliers winsorized
        """
        df_clean = df.copy()
        
        for col in columns:
            if col not in df.columns:
                print(f"Warning: Column '{col}' not found, skipping winsorization")
                continue
                
            # Get valid (non-NaN) data for percentile calculation
            valid_data = df[col].dropna()
            
            if len(valid_data) == 0:
                print(f"Warning: No valid data for column '{col}', skipping winsorization")
                continue
            
            # Calculate percentile bounds
            lower_bound, upper_bound = np.quantile(valid_data, self.winsorize_percentiles)
            
            # Store bounds for reporting
            self.outlier_bounds[col] = {
                'lower': lower_bound,
                'upper': upper_bound,
                'original_range': (valid_data.min(), valid_data.max())
            }
            
            # Apply winsorization
            outlier_mask = (df[col] < lower_bound) | (df[col] > upper_bound)
            outlier_count = outlier_mask.sum()
            
            # Set outliers to NaN (more conservative than clipping)
            df_clean.loc[outlier_mask, col] = np.nan
            
            print(f"Winsorized '{col}': bounds=[{lower_bound:.3f}, {upper_bound:.3f}], "
                  f"removed {outlier_count} outliers ({outlier_count/len(df)*100:.1f}%)")
        
        return df_clean
    
    def normalize_data_enhanced(self, df: pd.DataFrame, columns: List[str]) -> Tuple[pd.DataFrame, Dict]:
        """
        Enhanced normalization with better missing value handling and multi-well consistency.
        
        Args:
            df: Input dataframe
            columns: Columns to normalize
            
        Returns:
            Tuple of (normalized_df, scalers_dict)
        """
        df_scaled = df.copy()
        scalers = {}
        
        for col in columns:
            if col not in df.columns:
                print(f"Warning: Column '{col}' not found, skipping normalization")
                continue
            
            # Get valid data for fitting scaler
            valid_data = df[col].dropna()
            
            if len(valid_data) == 0:
                print(f"Warning: No valid data for column '{col}', skipping normalization")
                scalers[col] = None
                continue
            
            # Choose scaler based on method
            if self.normalization_method == 'standard':
                scaler = StandardScaler()
            elif self.normalization_method == 'robust':
                scaler = RobustScaler()
            else:
                raise ValueError(f"Unknown normalization method: {self.normalization_method}")
            
            # Fit scaler on valid data only
            scaler.fit(valid_data.values.reshape(-1, 1))
            
            # Transform all data (preserves NaN values)
            df_scaled[col] = scaler.transform(df[[col]])
            scalers[col] = scaler
            
            # Store normalization statistics
            self.preprocessing_stats[col] = {
                'original_mean': valid_data.mean(),
                'original_std': valid_data.std(),
                'valid_count': len(valid_data),
                'total_count': len(df[col]),
                'missing_rate': (len(df[col]) - len(valid_data)) / len(df[col])
            }
            
            print(f"Normalized '{col}': method={self.normalization_method}, "
                  f"valid_data={len(valid_data)}/{len(df[col])} "
                  f"({(1-self.preprocessing_stats[col]['missing_rate'])*100:.1f}%)")
        
        self.scalers = scalers
        return df_scaled, scalers
    
    def get_valid_intervals(self, data_array: np.ndarray, columns_to_check: List[int] = None) -> List[Tuple[int, int]]:
        """
        Identify continuous intervals without NaN values.
        Critical for sequence-based deep learning models.

        Args:
            data_array: Input array (samples x features)
            columns_to_check: List of column indices to check for validity.
                            If None, checks all columns.

        Returns:
            List of (start_idx, end_idx) tuples for valid intervals
        """
        # Determine which columns to check for validity
        if columns_to_check is None:
            check_array = data_array
        else:
            check_array = data_array[:, columns_to_check]

        # Check which rows have all valid (non-NaN) values in the specified columns
        all_valid = ~np.isnan(check_array).any(axis=1)

        # Find breakpoints where validity changes
        breaks = [0] + (np.where(all_valid[:-1] != all_valid[1:])[0] + 1).tolist() + [len(all_valid)]

        valid_intervals = []
        for i in range(len(breaks) - 1):
            start_idx = breaks[i]
            end_idx = breaks[i + 1]

            # Only store intervals that have valid data
            if all_valid[start_idx]:
                valid_intervals.append((start_idx, end_idx))

        return valid_intervals
    
    def create_sequences_enhanced(self, df: pd.DataFrame, well_col: str,
                                feature_cols: List[str]) -> Tuple[np.ndarray, List[Dict]]:
        """
        Create sequences with intelligent valid interval detection.
        Ensures sequences have continuous, complete data.

        Args:
            df: Input dataframe
            well_col: Column name for well identifier
            feature_cols: Feature column names

        Returns:
            Tuple of (sequences_array, metadata_list)
        """
        all_sequences = []
        metadata = []

        # Ensure we have the original index to work with
        df_reset = df.reset_index()

        # Detect if we're in prediction mode by checking if the last column (target) is mostly NaN
        # This happens when the target column is intentionally masked during prediction
        target_col_idx = len(feature_cols) - 1  # Assume target is the last column
        is_prediction_mode = False

        if len(feature_cols) > 1:  # Only check if we have multiple columns
            sample_well = df_reset[well_col].iloc[0]
            sample_data = df_reset[df_reset[well_col] == sample_well][feature_cols].values
            if sample_data.shape[1] > target_col_idx:
                target_nan_ratio = np.isnan(sample_data[:, target_col_idx]).mean()
                is_prediction_mode = target_nan_ratio > 0.95  # >95% NaN indicates prediction mode

        print(f"Sequence creation mode: {'PREDICTION' if is_prediction_mode else 'TRAINING'}")
        if is_prediction_mode:
            print("  - Using feature columns only for valid interval detection")
            print("  - Target column will be included in sequences as NaN for model prediction")

        # Import progress tracker
        try:
            from utils.well_progress import create_well_progress_tracker
            use_progress_bar = True
        except ImportError:
            use_progress_bar = False
            print("⚠️ Progress tracker not available, using basic logging")

        wells = df_reset[well_col].unique()

        # Initialize progress tracker
        if use_progress_bar:
            progress_tracker = create_well_progress_tracker(
                wells=list(wells),
                description="Creating sequences",
                show_current_well=True,
                suppress_warnings=True
            )
        else:
            progress_tracker = None

        for well in wells:
            # Start well processing
            if use_progress_bar:
                progress_tracker.start_well(well)

            try:
                well_df = df_reset[df_reset[well_col] == well].copy()
                well_data = well_df[feature_cols].values

                # Get valid intervals for this well
                if is_prediction_mode and len(feature_cols) > 1:
                    # In prediction mode, only check feature columns (exclude target)
                    feature_indices = list(range(len(feature_cols) - 1))  # All except last (target)
                    valid_intervals = self.get_valid_intervals(well_data, feature_indices)
                else:
                    # In training mode, check all columns
                    valid_intervals = self.get_valid_intervals(well_data)

                well_sequences = 0
                for start_idx, end_idx in valid_intervals:
                    interval_length = end_idx - start_idx

                    # Skip intervals too short for sequences
                    if interval_length < self.sequence_len:
                        continue

                    # Get the part of the dataframe for this interval
                    interval_df = well_df.iloc[start_idx:end_idx]

                    # Create sequences within this valid interval
                    current_pos = 0 # Position is relative to the start of the interval
                    while current_pos + self.sequence_len <= len(interval_df):
                        sequence_data = interval_df.iloc[current_pos:current_pos + self.sequence_len]
                        all_sequences.append(sequence_data[feature_cols].values)

                        # Store metadata, including the original dataframe indices
                        metadata.append({
                            'well': well,
                            'original_indices': sequence_data['index'].tolist(),
                            'sequence_id': len(all_sequences) - 1
                        })

                        current_pos += self.sequence_stride
                        well_sequences += 1

                # Finish well processing successfully
                if use_progress_bar:
                    progress_tracker.finish_well(
                        well_name=well,
                        intervals=len(valid_intervals),
                        sequences=well_sequences,
                        success=True
                    )
                else:
                    # Fallback to basic logging if progress tracker not available
                    print(f"Well '{well}': {len(valid_intervals)} valid intervals, "
                          f"{well_sequences} sequences created")

            except Exception as e:
                # Handle well processing errors
                error_msg = str(e)
                if use_progress_bar:
                    progress_tracker.log_error(well, error_msg)
                    progress_tracker.finish_well(
                        well_name=well,
                        intervals=0,
                        sequences=0,
                        success=False,
                        error_msg=error_msg
                    )
                else:
                    print(f"❌ Error processing well '{well}': {error_msg}")
                continue

        # Close progress tracker
        if use_progress_bar:
            progress_tracker.close()

        if not all_sequences:
            warnings.warn("No valid sequences could be created. Check data quality and sequence parameters.")
            return np.array([]), []

        sequences_array = np.array(all_sequences)

        # Print final summary (only if not using progress tracker, as it prints its own summary)
        if not use_progress_bar:
            print(f"Total sequences created: {len(sequences_array)} "
                  f"(shape: {sequences_array.shape})")

        return sequences_array, metadata

    def introduce_realistic_missingness(self, sequences: np.ndarray) -> np.ndarray:
        """
        Introduce realistic missing patterns for well log data.
        Uses chunked missing patterns that better reflect real-world scenarios.

        Args:
            sequences: Input sequences array (n_sequences, seq_len, n_features)

        Returns:
            Sequences with missing values introduced
        """
        sequences_with_missing = sequences.copy()
        n_sequences, seq_len, n_features = sequences.shape

        total_elements = np.prod(sequences.shape)
        n_missing = int(total_elements * self.missing_rate)

        # Strategy: Mix of random and chunked missing patterns
        # 30% random missing (sensor noise, bad readings)
        # 70% chunked missing (tool failures, bad hole conditions)

        random_missing = int(n_missing * 0.3)
        chunked_missing = n_missing - random_missing

        # Random missing values
        random_indices = np.random.choice(total_elements, random_missing, replace=False)

        # Chunked missing values (more realistic for well logs)
        chunk_indices = []
        chunk_sizes = np.random.randint(3, min(15, seq_len//2), size=chunked_missing//8)

        for chunk_size in chunk_sizes:
            if len(chunk_indices) >= chunked_missing:
                break

            # Random sequence and feature
            seq_idx = np.random.randint(0, n_sequences)
            feat_idx = np.random.randint(0, n_features)
            start_pos = np.random.randint(0, max(1, seq_len - chunk_size))

            # Add chunk indices
            for i in range(chunk_size):
                if start_pos + i < seq_len:
                    flat_idx = (seq_idx * seq_len * n_features +
                              (start_pos + i) * n_features + feat_idx)
                    chunk_indices.append(flat_idx)

        # Combine all missing indices
        all_missing_indices = list(set(list(random_indices) + chunk_indices[:chunked_missing]))

        # Apply missing values
        flat_view = sequences_with_missing.flatten()
        flat_view[all_missing_indices] = np.nan
        sequences_with_missing = flat_view.reshape(sequences.shape)

        actual_missing_rate = len(all_missing_indices) / total_elements
        print(f"Introduced {actual_missing_rate:.1%} missing values "
              f"({len(all_missing_indices)} elements)")
        print(f"Pattern: {random_missing} random + {len(all_missing_indices)-random_missing} chunked")

        return sequences_with_missing

    def validate_preprocessing_quality(self, sequences: np.ndarray,
                                     sequences_missing: np.ndarray) -> Dict:
        """
        Validate the quality of preprocessing for deep learning models.

        Args:
            sequences: Original sequences
            sequences_missing: Sequences with missing values

        Returns:
            Dictionary with quality metrics
        """
        quality_metrics = {}

        # Check for NaN values in original sequences
        nan_count_orig = np.isnan(sequences).sum()
        quality_metrics['original_nan_count'] = int(nan_count_orig)
        quality_metrics['original_nan_rate'] = float(nan_count_orig / np.prod(sequences.shape))

        # Check missing value introduction
        nan_count_missing = np.isnan(sequences_missing).sum()
        quality_metrics['missing_nan_count'] = int(nan_count_missing)
        quality_metrics['missing_nan_rate'] = float(nan_count_missing / np.prod(sequences_missing.shape))

        # Check data distribution (should be normalized)
        valid_data = sequences[~np.isnan(sequences)]
        if len(valid_data) > 0:
            quality_metrics['data_mean'] = float(np.mean(valid_data))
            quality_metrics['data_std'] = float(np.std(valid_data))
            quality_metrics['data_range'] = (float(np.min(valid_data)), float(np.max(valid_data)))

        # Check sequence continuity
        quality_metrics['total_sequences'] = sequences.shape[0]
        quality_metrics['sequence_length'] = sequences.shape[1]
        quality_metrics['n_features'] = sequences.shape[2]

        # Gradient-friendly checks
        if len(valid_data) > 0:
            quality_metrics['gradient_friendly'] = {
                'no_extreme_values': bool(np.abs(valid_data).max() < 10),
                'reasonable_std': bool(0.5 < np.std(valid_data) < 2.0),
                'no_infinite_values': bool(np.isfinite(valid_data).all())
            }

        return quality_metrics

    def get_preprocessing_report(self) -> str:
        """
        Generate a comprehensive preprocessing report.

        Returns:
            Formatted report string
        """
        report = []
        report.append("=" * 60)
        report.append(" ENHANCED PREPROCESSING REPORT")
        report.append("=" * 60)

        # Configuration
        report.append(f"\n📋 Configuration:")
        report.append(f"   • Winsorization percentiles: {self.winsorize_percentiles}")
        report.append(f"   • Normalization method: {self.normalization_method}")
        report.append(f"   • Sequence length: {self.sequence_len}")
        report.append(f"   • Sequence stride: {self.sequence_stride}")
        report.append(f"   • Missing rate: {self.missing_rate}")

        # Outlier bounds
        if self.outlier_bounds:
            report.append(f"\n🎯 Outlier Detection (Winsorization):")
            for col, bounds in self.outlier_bounds.items():
                orig_range = bounds['original_range']
                new_range = (bounds['lower'], bounds['upper'])
                report.append(f"   • {col}: {orig_range[0]:.3f}-{orig_range[1]:.3f} → "
                            f"{new_range[0]:.3f}-{new_range[1]:.3f}")

        # Normalization stats
        if self.preprocessing_stats:
            report.append(f"\n📊 Normalization Statistics:")
            for col, stats in self.preprocessing_stats.items():
                report.append(f"   • {col}: mean={stats['original_mean']:.3f}, "
                            f"std={stats['original_std']:.3f}, "
                            f"valid={stats['valid_count']}/{stats['total_count']} "
                            f"({(1-stats['missing_rate'])*100:.1f}%)")

        report.append("\n✅ Enhanced preprocessing completed successfully!")
        return "\n".join(report)


def enhanced_preprocessing_pipeline(df: pd.DataFrame,
                                  feature_cols: List[str],
                                  target_col: str,
                                  sequence_len: int = 64,
                                  sequence_stride: int = 32,
                                  missing_rate: float = 0.2,
                                  normalization_method: str = 'standard',
                                  winsorize_percentiles: Tuple[float, float] = (0.01, 0.99),
                                  random_seed: int = 42) -> Tuple[np.ndarray, np.ndarray, Dict, str]:
    """
    Complete enhanced preprocessing pipeline for deep learning models.
    Integrates cp_preconditioning techniques with deep learning best practices.

    Args:
        df: Input dataframe with well log data
        feature_cols: List of feature column names
        target_col: Target column name
        sequence_len: Length of sequences for deep learning
        sequence_stride: Stride between sequence starts
        missing_rate: Rate of missing values to introduce
        normalization_method: 'standard' or 'robust'
        winsorize_percentiles: Percentiles for outlier removal
        random_seed: Random seed for reproducibility

    Returns:
        Tuple of (clean_sequences, missing_sequences, scalers, report)
    """
    print("🚀 Starting Enhanced Preprocessing Pipeline...")

    # Initialize preprocessor
    preprocessor = EnhancedLogPreprocessor(
        winsorize_percentiles=winsorize_percentiles,
        normalization_method=normalization_method,
        sequence_len=sequence_len,
        sequence_stride=sequence_stride,
        missing_rate=missing_rate,
        random_seed=random_seed
    )

    # Step 1: Winsorize outliers
    print("\n🎯 Step 1: Statistical outlier removal (winsorization)...")
    all_cols = feature_cols + [target_col]
    df_winsorized = preprocessor.winsorize_outliers(df, all_cols)

    # Step 2: Enhanced normalization
    print("\n📊 Step 2: Enhanced normalization...")
    df_normalized, scalers = preprocessor.normalize_data_enhanced(df_winsorized, all_cols)

    # Step 3: Create sequences with valid interval detection
    print("\n🔗 Step 3: Intelligent sequence creation...")
    sequences, metadata = preprocessor.create_sequences_enhanced(
        df_normalized, 'WELL', all_cols
    )

    # Step 4: Introduce realistic missing patterns
    print("\n❓ Step 4: Realistic missing value introduction...")
    sequences_missing = preprocessor.introduce_realistic_missingness(sequences)

    # Step 5: Quality validation
    print("\n✅ Step 5: Quality validation...")
    quality_metrics = preprocessor.validate_preprocessing_quality(sequences, sequences_missing)

    # Generate report
    report = preprocessor.get_preprocessing_report()

    # Add quality metrics to report
    report += f"\n\n📈 Quality Metrics:"
    report += f"\n   • Original NaN rate: {quality_metrics['original_nan_rate']:.1%}"
    report += f"\n   • Missing NaN rate: {quality_metrics['missing_nan_rate']:.1%}"
    report += f"\n   • Data mean: {quality_metrics.get('data_mean', 'N/A'):.3f}"
    report += f"\n   • Data std: {quality_metrics.get('data_std', 'N/A'):.3f}"
    report += f"\n   • Total sequences: {quality_metrics['total_sequences']}"

    if 'gradient_friendly' in quality_metrics:
        gf = quality_metrics['gradient_friendly']
        report += f"\n   • Gradient-friendly: {all(gf.values())}"

    print(report)

    return sequences, sequences_missing, scalers, report


# Backward compatibility functions for existing workflow
def enhanced_normalize_data(df: pd.DataFrame, columns: List[str], scalers: Optional[Dict] = None) -> Tuple[pd.DataFrame, Dict]:
    """
    Drop-in replacement for existing normalize_data function with enhancements.

    Args:
        df: Input dataframe
        columns: Columns to normalize
        scalers: Optional pre-fitted scalers to use (for validation data)

    Returns:
        Tuple of (normalized_df, scalers_dict)
    """
    preprocessor = EnhancedLogPreprocessor()

    if scalers is not None:
        # Use existing scalers (for validation data)
        df_scaled = df.copy()
        for col in columns:
            if col in scalers and col in df.columns:
                valid_mask = ~df[col].isna()
                if valid_mask.any():
                    df_scaled.loc[valid_mask, col] = scalers[col].transform(
                        df[col][valid_mask].values.reshape(-1, 1)
                    ).flatten()
        return df_scaled, scalers
    else:
        # Fit new scalers (for training data)
        return preprocessor.normalize_data_enhanced(df, columns)


def enhanced_create_sequences(df: pd.DataFrame, well_col: str,
                            feature_cols: List[str], sequence_len: int = 64,
                            step: int = 1) -> np.ndarray:
    """
    Drop-in replacement for existing create_sequences function with enhancements.
    """
    preprocessor = EnhancedLogPreprocessor(
        sequence_len=sequence_len,
        sequence_stride=step
    )
    sequences, _ = preprocessor.create_sequences_enhanced(df, well_col, feature_cols)
    return sequences


def enhanced_introduce_missingness(sequences: np.ndarray, missing_rate: float = 0.2,
                                 random_seed: int = 42) -> np.ndarray:
    """
    Drop-in replacement for existing introduce_missingness function with enhancements.
    """
    preprocessor = EnhancedLogPreprocessor(missing_rate=missing_rate, random_seed=random_seed)
    return preprocessor.introduce_realistic_missingness(sequences)
