#!/usr/bin/env python3
"""
Test script to verify TransformerModel initialization and _estimate_parameters method
"""

import sys
import traceback

def test_transformer_initialization():
    """Test TransformerModel initialization with the problematic parameters."""
    try:
        from models.advanced_models.transformer_model import TransformerModel
        
        print("🧪 Testing TransformerModel initialization...")
        print("Parameters:")
        print("   - sequence_len=64")
        print("   - n_features=5") 
        print("   - d_model=256")
        print("   - n_heads=8")
        print("   - n_encoder_layers=6")
        print("   - d_ff=1024")
        print("   - dropout=0.1")
        print("   - use_prediction_only=True")
        print()
        
        # Test with the exact parameters mentioned in the error
        model = TransformerModel(
            sequence_len=64,
            n_features=5,
            d_model=256,
            n_heads=8,
            n_encoder_layers=6,
            d_ff=1024,
            dropout=0.1,
            use_prediction_only=True
        )
        
        print("✅ Model created successfully!")
        print(f"   - Total parameters: {model._estimate_parameters():,}")
        print(f"   - Memory estimate: {model._estimate_memory_mb():.1f} MB")
        print(f"   - Prediction-only mode: {model.prediction_only_mode}")
        print(f"   - Device: {model.device}")
        
        # Test the _estimate_parameters method specifically
        print("\n🔍 Testing _estimate_parameters method...")
        params = model._estimate_parameters()
        print(f"   - Method returned: {params:,} parameters")
        
        # Test with model not initialized (should use estimation)
        print("\n🔍 Testing parameter estimation without model...")
        model_copy = TransformerModel.__new__(TransformerModel)
        model_copy.n_encoder_layers = 6
        model_copy.d_model = 256
        model_copy.d_ff = 1024
        model_copy.n_features = 5
        model_copy.max_seq_len = 512
        model_copy.model = None
        
        estimated_params = model_copy._estimate_parameters()
        print(f"   - Estimation returned: {estimated_params:,} parameters")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   - Check if the models.advanced_models.transformer_model module exists")
        print("   - Verify the Python path includes the project root")
        return False
        
    except AttributeError as e:
        print(f"❌ Attribute Error: {e}")
        print("   - The _estimate_parameters method might be missing or incorrectly defined")
        traceback.print_exc()
        return False
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        print("   - Full traceback:")
        traceback.print_exc()
        return False

def test_base_model_compatibility():
    """Test if TransformerModel properly inherits from BaseAdvancedModel."""
    try:
        from models.advanced_models.transformer_model import TransformerModel
        from models.advanced_models.base_model import BaseAdvancedModel
        
        print("\n🔍 Testing BaseAdvancedModel compatibility...")
        
        # Check inheritance
        if issubclass(TransformerModel, BaseAdvancedModel):
            print("✅ TransformerModel properly inherits from BaseAdvancedModel")
        else:
            print("❌ TransformerModel does not inherit from BaseAdvancedModel")
            return False
        
        # Check if _estimate_parameters is defined in TransformerModel
        if hasattr(TransformerModel, '_estimate_parameters'):
            print("✅ _estimate_parameters method found in TransformerModel")
        else:
            print("❌ _estimate_parameters method not found in TransformerModel")
            return False
        
        # Check method signature
        import inspect
        sig = inspect.signature(TransformerModel._estimate_parameters)
        print(f"   - Method signature: {sig}")
        
        return True
        
    except Exception as e:
        print(f"❌ Compatibility test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting TransformerModel _estimate_parameters diagnostic tests")
    print("=" * 70)
    
    # Test 1: Base model compatibility
    test1_passed = test_base_model_compatibility()
    
    # Test 2: Model initialization
    test2_passed = test_transformer_initialization()
    
    print("\n" + "=" * 70)
    print("📊 Test Results Summary:")
    print(f"   - Base model compatibility: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   - Model initialization: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The _estimate_parameters method is working correctly.")
        print("   The error might be occurring in a different context or with different parameters.")
    else:
        print("\n⚠️ Some tests failed. The _estimate_parameters method needs investigation.")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
