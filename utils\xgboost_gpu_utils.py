"""
XGBoost GPU Configuration Utilities
Handles modern XGBoost GPU parameter syntax and fallback logic.
"""

import warnings
import xgboost as xgb
from typing import Dict, Any, Optional, Tuple
import torch


class XGBoostGPUManager:
    """
    Manages XGBoost GPU configuration with proper fallback handling.
    Handles the transition from deprecated parameters to modern syntax.
    """
    
    def __init__(self):
        """Initialize XGBoost GPU manager."""
        self.xgb_version = xgb.__version__
        self.cuda_support = self._check_cuda_support()
        self.gpu_available = self._check_gpu_availability()
        
        print(f"🔍 XGBoost GPU Manager initialized")
        print(f"   • XGBoost version: {self.xgb_version}")
        print(f"   • CUDA support: {'✅' if self.cuda_support else '❌'}")
        print(f"   • GPU available: {'✅' if self.gpu_available else '❌'}")
    
    def _check_cuda_support(self) -> bool:
        """Check if XGBoost was compiled with CUDA support."""
        try:
            # Try to create a simple GPU-enabled model to test CUDA support
            test_params = {
                'tree_method': 'hist',
                'device': 'cuda',
                'n_estimators': 1,
                'verbosity': 0
            }
            
            # Create a minimal test model
            import numpy as np
            X_test = np.array([[1, 2], [3, 4]])
            y_test = np.array([1, 2])
            
            model = xgb.XGBRegressor(**test_params)
            model.fit(X_test, y_test)
            
            return True
            
        except Exception as e:
            error_msg = str(e).lower()
            if any(phrase in error_msg for phrase in [
                'not compiled with cuda',
                'cuda support',
                'no visible gpu',
                'device ordinal',
                'must have at least one device'
            ]):
                return False
            else:
                # Other errors might not be CUDA-related
                print(f"⚠️ XGBoost GPU test failed with unexpected error: {e}")
                return False
    
    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available for XGBoost."""
        if not self.cuda_support:
            return False
        
        # Check PyTorch CUDA availability as a proxy
        return torch.cuda.is_available()
    
    def get_optimal_config(self, base_params: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
        """
        Get optimal XGBoost configuration with proper GPU/CPU fallback.
        
        Args:
            base_params: Base parameters for XGBoost
            
        Returns:
            Tuple of (optimized_params, is_gpu_enabled)
        """
        optimized_params = base_params.copy()
        
        # Remove any deprecated parameters
        deprecated_params = ['gpu_id', 'predictor']
        for param in deprecated_params:
            if param in optimized_params:
                del optimized_params[param]
                print(f"🔄 Removed deprecated parameter: {param}")
        
        if self.cuda_support and self.gpu_available:
            # Use modern GPU configuration
            optimized_params.update({
                'tree_method': 'hist',
                'device': 'cuda',
            })
            print("🚀 XGBoost: Using GPU acceleration with modern parameters")
            return optimized_params, True
        else:
            # Use CPU configuration
            optimized_params.update({
                'tree_method': 'hist',
                'device': 'cpu',
            })
            
            # Remove any GPU-specific parameters that might cause issues
            gpu_specific_params = ['gpu_platform_id', 'gpu_device_id']
            for param in gpu_specific_params:
                if param in optimized_params:
                    del optimized_params[param]
            
            reason = "CUDA not compiled" if not self.cuda_support else "GPU not available"
            print(f"💻 XGBoost: Using CPU configuration ({reason})")
            return optimized_params, False
    
    def create_safe_model(self, model_class, base_params: Dict[str, Any], **kwargs):
        """
        Create XGBoost model with safe GPU/CPU configuration.
        
        Args:
            model_class: XGBoost model class (XGBRegressor, XGBClassifier, etc.)
            base_params: Base parameters
            **kwargs: Additional keyword arguments
            
        Returns:
            Configured XGBoost model
        """
        # Get optimal configuration
        optimized_params, is_gpu = self.get_optimal_config(base_params)
        
        # Merge with additional kwargs
        final_params = {**optimized_params, **kwargs}
        
        try:
            # Try to create the model
            model = model_class(**final_params)
            
            # Test the model with a simple fit to ensure it works
            self._test_model_creation(model)
            
            print(f"✅ XGBoost model created successfully ({'GPU' if is_gpu else 'CPU'} mode)")
            return model
            
        except Exception as e:
            print(f"❌ XGBoost model creation failed: {e}")
            
            # If GPU mode failed, try CPU fallback
            if is_gpu:
                print("🔄 Attempting CPU fallback...")
                cpu_params, _ = self.get_optimal_config(base_params)
                cpu_params.update({
                    'tree_method': 'hist',
                    'device': 'cpu'
                })
                
                # Remove any remaining GPU parameters
                gpu_params_to_remove = ['gpu_id', 'predictor', 'gpu_platform_id', 'gpu_device_id']
                for param in gpu_params_to_remove:
                    cpu_params.pop(param, None)
                
                final_cpu_params = {**cpu_params, **kwargs}
                
                try:
                    model = model_class(**final_cpu_params)
                    self._test_model_creation(model)
                    print("✅ XGBoost model created with CPU fallback")
                    return model
                except Exception as cpu_e:
                    print(f"❌ CPU fallback also failed: {cpu_e}")
                    raise cpu_e
            else:
                raise e
    
    def _test_model_creation(self, model):
        """Test model creation with minimal data."""
        try:
            import numpy as np
            X_test = np.array([[1, 2], [3, 4], [5, 6], [7, 8]])
            y_test = np.array([1, 2, 3, 4])

            # Create a copy of the model for testing
            test_params = model.get_params().copy()

            # Remove early stopping for test to avoid validation dataset requirement
            if 'early_stopping_rounds' in test_params:
                test_params.pop('early_stopping_rounds')

            # Ensure minimal configuration for testing
            test_params.update({
                'n_estimators': 3,  # Very small for quick test
                'verbosity': 0      # Suppress output
            })

            test_model = model.__class__(**test_params)
            test_model.fit(X_test, y_test)

        except Exception as e:
            # If test fails, it might indicate configuration issues
            raise RuntimeError(f"Model configuration test failed: {e}")
    
    def get_version_info(self) -> Dict[str, Any]:
        """Get detailed version and capability information."""
        info = {
            'xgboost_version': self.xgb_version,
            'cuda_support': self.cuda_support,
            'gpu_available': self.gpu_available,
            'torch_cuda_available': torch.cuda.is_available(),
            'recommended_config': {}
        }
        
        if self.cuda_support and self.gpu_available:
            info['recommended_config'] = {
                'tree_method': 'hist',
                'device': 'cuda'
            }
        else:
            info['recommended_config'] = {
                'tree_method': 'hist',
                'device': 'cpu'
            }
        
        return info
    
    def print_configuration_report(self):
        """Print a detailed configuration report."""
        print("\n" + "="*60)
        print("🔍 XGBOOST GPU CONFIGURATION REPORT")
        print("="*60)
        
        info = self.get_version_info()
        
        print(f"📊 Version Information:")
        print(f"   • XGBoost version: {info['xgboost_version']}")
        print(f"   • PyTorch CUDA available: {'✅' if info['torch_cuda_available'] else '❌'}")
        
        print(f"\n🚀 GPU Support:")
        print(f"   • XGBoost CUDA compiled: {'✅' if info['cuda_support'] else '❌'}")
        print(f"   • GPU available: {'✅' if info['gpu_available'] else '❌'}")
        
        print(f"\n⚙️ Recommended Configuration:")
        for key, value in info['recommended_config'].items():
            print(f"   • {key}: {value}")
        
        print(f"\n💡 Status:")
        if info['cuda_support'] and info['gpu_available']:
            print("   ✅ GPU acceleration available and recommended")
        elif not info['cuda_support']:
            print("   ⚠️ XGBoost not compiled with CUDA support - using CPU")
            print("   💡 Consider installing xgboost-gpu or recompiling with CUDA")
        elif not info['gpu_available']:
            print("   ⚠️ GPU not available - using CPU")
        else:
            print("   💻 Using CPU configuration")
        
        print("="*60)


# Global XGBoost GPU manager instance
_global_xgb_manager = None

def get_xgboost_gpu_manager() -> XGBoostGPUManager:
    """Get the global XGBoost GPU manager instance."""
    global _global_xgb_manager
    if _global_xgb_manager is None:
        _global_xgb_manager = XGBoostGPUManager()
    return _global_xgb_manager

def create_optimized_xgboost_model(model_class, base_params: Dict[str, Any], **kwargs):
    """
    Create an optimized XGBoost model with proper GPU/CPU configuration.
    
    Args:
        model_class: XGBoost model class
        base_params: Base parameters
        **kwargs: Additional parameters
        
    Returns:
        Configured XGBoost model
    """
    manager = get_xgboost_gpu_manager()
    return manager.create_safe_model(model_class, base_params, **kwargs)

def get_xgboost_optimal_params(base_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get optimal XGBoost parameters with proper GPU/CPU configuration.
    
    Args:
        base_params: Base parameters
        
    Returns:
        Optimized parameters
    """
    manager = get_xgboost_gpu_manager()
    optimized_params, _ = manager.get_optimal_config(base_params)
    return optimized_params

def check_xgboost_gpu_support() -> bool:
    """
    Check if XGBoost GPU support is available.
    
    Returns:
        True if GPU support is available, False otherwise
    """
    manager = get_xgboost_gpu_manager()
    return manager.cuda_support and manager.gpu_available
