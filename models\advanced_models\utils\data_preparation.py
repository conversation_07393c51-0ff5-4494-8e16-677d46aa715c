"""
Data Preparation Utilities for Advanced Deep Learning Models
Provides PyPOTS data format utilities and preprocessing functions.
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, Tuple, Union
import warnings

def prepare_pypots_dataset(data: Union[torch.Tensor, np.ndarray], 
                          truth_data: Optional[Union[torch.Tensor, np.ndarray]] = None,
                          feature_names: Optional[list] = None) -> Dict[str, Any]:
    """
    Prepare data in PyPOTS format for training or prediction.
    
    Args:
        data: Input data with missing values (batch, sequence, features)
        truth_data: Ground truth data (optional, for training)
        feature_names: Names of features (optional)
        
    Returns:
        Dictionary in PyPOTS format
    """
    # Convert to numpy if needed
    if isinstance(data, torch.Tensor):
        data = data.cpu().numpy()
    if truth_data is not None and isinstance(truth_data, torch.Tensor):
        truth_data = truth_data.cpu().numpy()
    
    # Validate data shape
    if len(data.shape) != 3:
        raise ValueError(f"Expected 3D data (batch, sequence, features), got shape {data.shape}")
    
    batch_size, seq_len, n_features = data.shape
    
    # Create indicating mask (1 where data is observed, 0 where missing)
    indicating_mask = ~np.isnan(data)
    
    # Prepare PyPOTS dataset
    dataset = {
        'X': data,
        'indicating_mask': indicating_mask
    }
    
    # Add ground truth if provided (for training)
    if truth_data is not None:
        if truth_data.shape != data.shape:
            raise ValueError(f"Truth data shape {truth_data.shape} doesn't match input shape {data.shape}")
        dataset['X_intact'] = truth_data
    
    # Add feature names if provided
    if feature_names is not None:
        if len(feature_names) != n_features:
            warnings.warn(f"Feature names length {len(feature_names)} doesn't match n_features {n_features}")
        dataset['feature_names'] = feature_names
    
    # Add metadata
    dataset['metadata'] = {
        'batch_size': batch_size,
        'sequence_length': seq_len,
        'n_features': n_features,
        'missing_rate': np.isnan(data).mean(),
        'total_observations': batch_size * seq_len * n_features,
        'missing_observations': np.isnan(data).sum()
    }
    
    print(f"📊 PyPOTS dataset prepared:")
    print(f"   Shape: {data.shape}")
    print(f"   Missing rate: {dataset['metadata']['missing_rate']:.2%}")
    print(f"   Has ground truth: {truth_data is not None}")
    
    return dataset

def validate_pypots_dataset(dataset: Dict[str, Any]) -> bool:
    """
    Validate PyPOTS dataset format.
    
    Args:
        dataset: PyPOTS dataset dictionary
        
    Returns:
        bool: True if valid, False otherwise
    """
    required_keys = ['X', 'indicating_mask']
    
    # Check required keys
    for key in required_keys:
        if key not in dataset:
            print(f"❌ Missing required key: {key}")
            return False
    
    # Check data types and shapes
    X = dataset['X']
    mask = dataset['indicating_mask']
    
    if not isinstance(X, np.ndarray):
        print(f"❌ X should be numpy array, got {type(X)}")
        return False
    
    if not isinstance(mask, np.ndarray):
        print(f"❌ indicating_mask should be numpy array, got {type(mask)}")
        return False
    
    if X.shape != mask.shape:
        print(f"❌ X shape {X.shape} doesn't match mask shape {mask.shape}")
        return False
    
    if len(X.shape) != 3:
        print(f"❌ Expected 3D data, got shape {X.shape}")
        return False
    
    # Check ground truth if present
    if 'X_intact' in dataset:
        X_intact = dataset['X_intact']
        if not isinstance(X_intact, np.ndarray):
            print(f"❌ X_intact should be numpy array, got {type(X_intact)}")
            return False
        if X_intact.shape != X.shape:
            print(f"❌ X_intact shape {X_intact.shape} doesn't match X shape {X.shape}")
            return False
    
    print("✅ PyPOTS dataset validation passed")
    return True

def convert_sequences_to_pypots(sequences: np.ndarray, 
                               truth_sequences: Optional[np.ndarray] = None,
                               feature_names: Optional[list] = None) -> Dict[str, Any]:
    """
    Convert sequence data to PyPOTS format.
    
    Args:
        sequences: Sequence data (batch, sequence, features)
        truth_sequences: Ground truth sequences (optional)
        feature_names: Feature names (optional)
        
    Returns:
        PyPOTS dataset dictionary
    """
    return prepare_pypots_dataset(sequences, truth_sequences, feature_names)

def add_artificial_missingness(data: np.ndarray, 
                              missing_rate: float = 0.3,
                              missing_pattern: str = 'random',
                              seed: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    Add artificial missingness to complete data for training/testing.
    
    Args:
        data: Complete data array
        missing_rate: Proportion of values to make missing
        missing_pattern: Type of missingness ('random', 'block', 'feature')
        seed: Random seed for reproducibility
        
    Returns:
        Tuple of (data_with_missing, missing_mask)
    """
    if seed is not None:
        np.random.seed(seed)
    
    data_missing = data.copy()
    missing_mask = np.zeros_like(data, dtype=bool)
    
    if missing_pattern == 'random':
        # Random missingness
        total_elements = data.size
        n_missing = int(total_elements * missing_rate)
        flat_indices = np.random.choice(total_elements, n_missing, replace=False)
        flat_mask = np.zeros(total_elements, dtype=bool)
        flat_mask[flat_indices] = True
        missing_mask = flat_mask.reshape(data.shape)
        
    elif missing_pattern == 'block':
        # Block missingness (consecutive missing values)
        batch_size, seq_len, n_features = data.shape
        for b in range(batch_size):
            for f in range(n_features):
                if np.random.random() < missing_rate:
                    # Create a random block of missing values
                    block_start = np.random.randint(0, seq_len // 2)
                    block_length = np.random.randint(1, seq_len // 3)
                    block_end = min(block_start + block_length, seq_len)
                    missing_mask[b, block_start:block_end, f] = True
                    
    elif missing_pattern == 'feature':
        # Feature-wise missingness (entire features missing)
        batch_size, seq_len, n_features = data.shape
        for b in range(batch_size):
            n_missing_features = int(n_features * missing_rate)
            if n_missing_features > 0:
                missing_features = np.random.choice(n_features, n_missing_features, replace=False)
                missing_mask[b, :, missing_features] = True
    
    else:
        raise ValueError(f"Unknown missing pattern: {missing_pattern}")
    
    # Apply missingness
    data_missing[missing_mask] = np.nan
    
    print(f"🎭 Added {missing_pattern} missingness:")
    print(f"   Target rate: {missing_rate:.2%}")
    print(f"   Actual rate: {missing_mask.mean():.2%}")
    print(f"   Missing values: {missing_mask.sum()}")
    
    return data_missing, missing_mask

def normalize_pypots_data(dataset: Dict[str, Any], 
                         method: str = 'standard',
                         fit_on_observed: bool = True) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Normalize PyPOTS dataset.
    
    Args:
        dataset: PyPOTS dataset
        method: Normalization method ('standard', 'minmax', 'robust')
        fit_on_observed: Whether to fit normalization only on observed values
        
    Returns:
        Tuple of (normalized_dataset, normalization_params)
    """
    from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
    
    # Choose scaler
    if method == 'standard':
        scaler_class = StandardScaler
    elif method == 'minmax':
        scaler_class = MinMaxScaler
    elif method == 'robust':
        scaler_class = RobustScaler
    else:
        raise ValueError(f"Unknown normalization method: {method}")
    
    X = dataset['X'].copy()
    mask = dataset['indicating_mask']
    batch_size, seq_len, n_features = X.shape
    
    # Normalize each feature separately
    scalers = {}
    for f in range(n_features):
        # Get observed values for this feature
        if fit_on_observed:
            observed_values = X[:, :, f][mask[:, :, f]]
        else:
            observed_values = X[:, :, f].flatten()
            observed_values = observed_values[~np.isnan(observed_values)]
        
        if len(observed_values) > 0:
            # Fit scaler on observed values
            scaler = scaler_class()
            scaler.fit(observed_values.reshape(-1, 1))
            
            # Transform all values (including NaN)
            feature_data = X[:, :, f].flatten()
            valid_mask = ~np.isnan(feature_data)
            
            if valid_mask.sum() > 0:
                feature_data[valid_mask] = scaler.transform(
                    feature_data[valid_mask].reshape(-1, 1)
                ).flatten()
            
            X[:, :, f] = feature_data.reshape(batch_size, seq_len)
            scalers[f] = scaler
        else:
            print(f"⚠️ No observed values for feature {f}, skipping normalization")
            scalers[f] = None
    
    # Create normalized dataset
    normalized_dataset = dataset.copy()
    normalized_dataset['X'] = X
    
    # Normalize ground truth if present
    if 'X_intact' in dataset:
        X_intact = dataset['X_intact'].copy()
        for f in range(n_features):
            if scalers[f] is not None:
                feature_data = X_intact[:, :, f].flatten()
                valid_mask = ~np.isnan(feature_data)
                if valid_mask.sum() > 0:
                    feature_data[valid_mask] = scalers[f].transform(
                        feature_data[valid_mask].reshape(-1, 1)
                    ).flatten()
                X_intact[:, :, f] = feature_data.reshape(batch_size, seq_len)
        normalized_dataset['X_intact'] = X_intact
    
    normalization_params = {
        'method': method,
        'scalers': scalers,
        'fit_on_observed': fit_on_observed
    }
    
    print(f"🔧 Applied {method} normalization to PyPOTS dataset")
    
    return normalized_dataset, normalization_params

def denormalize_pypots_predictions(predictions: np.ndarray,
                                  normalization_params: Dict[str, Any]) -> np.ndarray:
    """
    Denormalize predictions back to original scale.
    
    Args:
        predictions: Normalized predictions
        normalization_params: Normalization parameters from normalize_pypots_data
        
    Returns:
        Denormalized predictions
    """
    if normalization_params is None:
        return predictions
    
    scalers = normalization_params['scalers']
    predictions_denorm = predictions.copy()
    
    batch_size, seq_len, n_features = predictions.shape
    
    for f in range(n_features):
        if scalers[f] is not None:
            feature_data = predictions_denorm[:, :, f].flatten()
            valid_mask = ~np.isnan(feature_data)
            
            if valid_mask.sum() > 0:
                feature_data[valid_mask] = scalers[f].inverse_transform(
                    feature_data[valid_mask].reshape(-1, 1)
                ).flatten()
            
            predictions_denorm[:, :, f] = feature_data.reshape(batch_size, seq_len)
    
    print("🔄 Denormalized predictions to original scale")
    
    return predictions_denorm
