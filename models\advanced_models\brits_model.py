"""
BRITS (Bidirectional Recurrent Imputation Time Series) Model Implementation
Specialized for temporal dependencies in well log data

This module implements the BRITS model for well log imputation, leveraging
bidirectional RNN architecture for temporal pattern modeling.
"""

import torch
import numpy as np
from typing import Dict, Any, Optional
import warnings

# Import PyPOTS components with error handling
try:
    from pypots.imputation import BRITS
    from pypots.optim import Adam
    PYPOTS_AVAILABLE = True
except ImportError as e:
    PYPOTS_AVAILABLE = False
    warnings.warn(f"PyPOTS not available: {e}")
    BRITS = None
    Adam = None

# Import GPU utilities
try:
    from utils.gpu_acceleration import GPUManager
    from utils.performance_monitor import get_performance_monitor
    from utils.gpu_fallback import get_fallback_manager, safe_cuda_empty_cache
    GPU_UTILS_AVAILABLE = True
except ImportError:
    print("⚠️ GPU utilities not available for BRITS model")
    GPU_UTILS_AVAILABLE = False
    GPUManager = None
    get_performance_monitor = None
    get_fallback_manager = None
    safe_cuda_empty_cache = lambda: None

from .base_model import BaseAdvancedModel

class BRITSModel(BaseAdvancedModel):
    """
    BRITS model wrapper for well log imputation.
    Leverages bidirectional RNN for temporal pattern modeling.
    
    BRITS (Bidirectional Recurrent Imputation Time Series) is specifically
    designed for time series imputation using bidirectional RNNs with
    temporal decay factors and feature correlation modeling.
    """

    def __init__(self, n_features=4, sequence_len=64, rnn_hidden_size=128,
                 epochs=50, batch_size=32, learning_rate=1e-3, device=None,
                 use_mixed_precision=True, **kwargs):
        """
        Initialize BRITS model with GPU support.

        Args:
            n_features: Number of log features
            sequence_len: Length of input sequences
            rnn_hidden_size: Hidden size for RNN layers
            epochs: Training epochs
            batch_size: Training batch size
            learning_rate: Optimizer learning rate
            device: Device to use ('cuda', 'cpu', or None for auto-detection)
            use_mixed_precision: Whether to use automatic mixed precision training
            **kwargs: Additional model parameters
        """
        super().__init__(n_features, sequence_len, epochs, batch_size, learning_rate, **kwargs)
        
        if not PYPOTS_AVAILABLE:
            raise ImportError("PyPOTS is required for BRITS model. Please install with: pip install pypots")
        
        self.rnn_hidden_size = rnn_hidden_size
        self.use_mixed_precision = use_mixed_precision

        # Initialize GPU management with fallback
        self.gpu_manager = None
        self.fallback_manager = get_fallback_manager() if GPU_UTILS_AVAILABLE else None
        self.device = self._setup_device(device)
        self.performance_monitor = None

        # Validate parameters
        self._validate_parameters()

        print(f"🎯 BRITS Model Configuration:")
        print(f"   - RNN hidden size: {rnn_hidden_size}")
        print(f"   - Bidirectional: True")
        print(f"   - Temporal decay: Enabled")
        print(f"   - Device: {self.device}")
        print(f"   - Mixed precision: {use_mixed_precision and self.device == 'cuda'}")

        # Initialize the model
        self._initialize_model()

    def _setup_device(self, device=None):
        """
        Setup the computing device (GPU/CPU) with fallback mechanisms.

        Args:
            device: Requested device ('cuda', 'cpu', or None for auto-detection)

        Returns:
            str: The selected device string for PyPOTS
        """
        # Use fallback manager if available
        if self.fallback_manager:
            if device is not None:
                safe_device = self.fallback_manager.get_safe_device(device)
            else:
                safe_device = self.fallback_manager.get_safe_device('cuda')

            if safe_device.type == 'cuda':
                # Initialize GPU manager if utilities are available
                if GPU_UTILS_AVAILABLE:
                    try:
                        self.gpu_manager = GPUManager()
                        print(f"🚀 BRITS using GPU with fallback protection: {safe_device}")
                        return 'cuda'  # PyPOTS expects string
                    except Exception as e:
                        print(f"⚠️ GPU manager initialization failed: {e}, using basic CUDA with fallback")

                # Basic CUDA setup with fallback protection
                try:
                    gpu_name = torch.cuda.get_device_name(0)
                    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
                    print(f"🚀 BRITS using GPU with fallback protection: {gpu_name} ({gpu_memory:.1f} GB)")
                except Exception as e:
                    print(f"⚠️ GPU info retrieval failed: {e}")
                return 'cuda'  # PyPOTS expects string
            else:
                print("💻 BRITS using CPU (GPU fallback activated)")
                return 'cpu'  # PyPOTS expects string

        # Fallback to original logic if fallback manager not available
        if device is not None:
            if device == 'cuda' and not torch.cuda.is_available():
                print("⚠️ CUDA requested but not available, falling back to CPU")
                return 'cpu'
            return device if isinstance(device, str) else str(device)

        # Auto-detect best device
        if torch.cuda.is_available():
            try:
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
                print(f"🚀 BRITS using GPU: {gpu_name} ({gpu_memory:.1f} GB)")
                return 'cuda'
            except Exception as e:
                print(f"⚠️ GPU setup failed: {e}, falling back to CPU")
                return 'cpu'
        else:
            print("💻 BRITS using CPU (CUDA not available)")
            return 'cpu'

    def _validate_parameters(self):
        """Validate model parameters for well log data."""
        if self.rnn_hidden_size < 16:
            print("⚠️ Warning: rnn_hidden_size < 16 may limit model capacity")
        
        if self.sequence_len < 8:
            print("⚠️ Warning: sequence_len < 8 may not capture temporal patterns effectively")
            
        if self.rnn_hidden_size > 512:
            print(f"⚠️ Warning: rnn_hidden_size={self.rnn_hidden_size} may be too large (recommended: 64-512)")

    def _initialize_model(self) -> None:
        """Initialize the PyPOTS BRITS model."""
        try:
            print(f"🔧 Initializing BRITS model...")
            
            # Adjust patience to be less than epochs
            patience = min(15, max(1, self.epochs // 3))

            self.model = BRITS(
                n_steps=self.sequence_len,
                n_features=self.n_features,
                rnn_hidden_size=self.rnn_hidden_size,
                batch_size=self.batch_size,
                epochs=self.epochs,
                patience=patience,  # Adjusted early stopping patience
                optimizer=Adam(lr=self.learning_rate),
                device=self.device,  # Use GPU-optimized device selection
                saving_path=None,
                model_saving_strategy=None
            )
            
            print(f"✅ BRITS model initialized successfully")
            print(f"   - Parameters: ~{self._estimate_parameters():,}")
            print(f"   - Memory usage: ~{self._estimate_memory_mb():.1f} MB")
            
        except Exception as e:
            print(f"❌ Failed to initialize BRITS model: {e}")
            raise RuntimeError(f"BRITS model initialization failed: {e}")

    def fit(self, train_data: torch.Tensor, truth_data: torch.Tensor,
            epochs: Optional[int] = None, batch_size: Optional[int] = None,
            patience: int = 10, min_delta: float = 1e-4) -> None:
        """
        Train the BRITS model using PyPOTS native training interface.

        Args:
            train_data: Training data with missing values
            truth_data: Complete ground truth data
            epochs: Number of epochs (optional override)
            batch_size: Batch size (optional override)
            patience: Patience for early stopping
            min_delta: Minimum change for early stopping
        """
        import time
        
        print(f"🚀 Starting BRITS training...")
        print(f"   • Training data shape: {train_data.shape}")
        print(f"   • Truth data shape: {truth_data.shape}")
        
        # Initialize performance monitoring if available
        if GPU_UTILS_AVAILABLE and get_performance_monitor:
            self.performance_monitor = get_performance_monitor()
            self.performance_monitor.start_monitoring()
            print("📊 Performance monitoring started for BRITS training")

        start_time = time.time()
        
        try:
            # Prepare data in PyPOTS format
            train_set = self._prepare_data(train_data, truth_data)
            
            print(f"   • Prepared training set with keys: {list(train_set.keys())}")
            
            # Use PyPOTS native training interface
            print(f"   • Using PyPOTS BRITS.fit() method...")
            
            # PyPOTS BRITS expects data in specific format
            # The model.fit() method handles the training internally
            self.model.fit(train_set)
            
            # Mark as fitted
            self.is_fitted = True
            
            # Record training time
            training_time = time.time() - start_time
            
            # Clear GPU cache if available
            if self.device == 'cuda':
                safe_cuda_empty_cache()
                
            print(f"✅ BRITS training completed successfully in {training_time:.2f} seconds!")
            
            # Print GPU memory usage if available
            if self.device == 'cuda' and torch.cuda.is_available():
                try:
                    current_memory = torch.cuda.memory_allocated() / 1024**2
                    max_memory = torch.cuda.max_memory_allocated() / 1024**2
                    print(f"   • Current GPU memory: {current_memory:.1f} MB")
                    print(f"   • Peak GPU memory: {max_memory:.1f} MB")
                except Exception as e:
                    print(f"   • GPU memory info unavailable: {e}")

        except Exception as e:
            print(f"❌ BRITS training failed: {e}")
            print(f"   • Error type: {type(e).__name__}")
            
            # Try to provide helpful error information
            if "CUDA" in str(e):
                print("   • This appears to be a CUDA-related error")
                print("   • Try reducing batch_size or switching to CPU")
            elif "memory" in str(e).lower():
                print("   • This appears to be a memory-related error")
                print("   • Try reducing batch_size or sequence_len")
            
            raise RuntimeError(f"BRITS training failed: {e}")

        finally:
            # Stop monitoring and print report
            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()
                if self.device == 'cuda':
                    print("\n📊 BRITS Training Performance Summary:")
                    summary = self.performance_monitor.get_performance_summary()
                    if summary['gpu_stats']:
                        gpu = summary['gpu_stats']
                        print(f"   • Average GPU Utilization: {gpu['avg_utilization']:.1f}%")
                        print(f"   • Peak GPU Memory: {gpu['max_memory_used']:.1f}GB")
                    if summary['training_stats']:
                        train = summary['training_stats']
                        print(f"   • Total Training Time: {train['total_training_time']:.1f}s")

    def _prepare_data(self, data: torch.Tensor, truth_data: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """Prepare data in PyPOTS format for BRITS."""
        return self._prepare_pypots_data(data, truth_data)
    
    def _estimate_parameters(self) -> int:
        """Estimate the number of model parameters."""
        # Estimate parameters for bidirectional RNN
        rnn_params = (
            # Forward RNN (LSTM gates: input, forget, output, cell)
            4 * (self.n_features * self.rnn_hidden_size + 
                 self.rnn_hidden_size * self.rnn_hidden_size + 
                 self.rnn_hidden_size) +
            # Backward RNN  
            4 * (self.n_features * self.rnn_hidden_size + 
                 self.rnn_hidden_size * self.rnn_hidden_size + 
                 self.rnn_hidden_size) +
            # Output layers and decay mechanisms
            2 * self.rnn_hidden_size * self.n_features +
            # Additional BRITS-specific parameters (decay, imputation layers)
            self.n_features * self.rnn_hidden_size * 2
        )
        
        return rnn_params
    
    def _estimate_memory_mb(self) -> float:
        """Estimate memory usage in MB."""
        # Rough estimation based on model size and batch size
        param_memory = self._estimate_parameters() * 4 / (1024 * 1024)  # 4 bytes per float32
        activation_memory = (
            self.batch_size * self.sequence_len * self.rnn_hidden_size * 
            4 / (1024 * 1024)  # Bidirectional + hidden states
        )
        return param_memory + activation_memory
    
    def get_temporal_patterns(self, data: torch.Tensor) -> Optional[Dict[str, np.ndarray]]:
        """
        Extract temporal patterns learned by the model.
        
        Args:
            data: Input data tensor
            
        Returns:
            Dictionary with temporal pattern information or None if not available
        """
        if not self.is_fitted:
            print("⚠️ Model must be fitted before extracting temporal patterns")
            return None
            
        try:
            # This would require modification of PyPOTS BRITS to expose hidden states
            # For now, return None and implement in future versions
            print("ℹ️ Temporal pattern extraction not yet implemented")
            return None
        except Exception as e:
            print(f"⚠️ Failed to extract temporal patterns: {e}")
            return None
    
    def get_model_complexity(self) -> Dict[str, Any]:
        """Get model complexity metrics."""
        return {
            'total_parameters': self._estimate_parameters(),
            'rnn_hidden_size': self.rnn_hidden_size,
            'bidirectional': True,
            'complexity_score': 2,  # Medium complexity
            'memory_mb': self._estimate_memory_mb(),
            'computational_cost': 'medium',
            'performance_tier': 'high'
        }
    
    def get_hyperparameter_ranges(self) -> Dict[str, Dict[str, Any]]:
        """Get recommended hyperparameter ranges for optimization."""
        return {
            'rnn_hidden_size': {'min': 32, 'max': 512, 'default': 128, 'type': 'int', 'step': 32},
            'learning_rate': {'min': 1e-5, 'max': 1e-2, 'default': 1e-3, 'type': 'float'},
            'batch_size': {'min': 8, 'max': 128, 'default': 32, 'type': 'int'},
            'epochs': {'min': 10, 'max': 200, 'default': 50, 'type': 'int'}
        }
    
    def optimize_for_dataset(self, data_shape: tuple, missing_rate: float = 0.3) -> Dict[str, Any]:
        """
        Suggest optimal hyperparameters based on dataset characteristics.
        
        Args:
            data_shape: Shape of the dataset (batch, sequence, features)
            missing_rate: Proportion of missing values
            
        Returns:
            Dictionary with suggested hyperparameters
        """
        batch_size, seq_len, n_feat = data_shape
        
        # Adjust RNN size based on sequence length and features
        if seq_len < 32:
            suggested_hidden_size = 64
        elif seq_len < 64:
            suggested_hidden_size = 128
        else:
            suggested_hidden_size = 256
            
        # Adjust based on number of features
        suggested_hidden_size = max(suggested_hidden_size, n_feat * 16)
        
        # Adjust based on missing rate
        if missing_rate > 0.5:
            suggested_epochs = 100  # More epochs for high missing rates
        else:
            suggested_epochs = 50
            
        # Adjust batch size based on available data
        if batch_size < 100:
            suggested_batch_size = min(16, batch_size // 2)
        else:
            suggested_batch_size = 32
            
        return {
            'rnn_hidden_size': min(suggested_hidden_size, 512),
            'epochs': suggested_epochs,
            'batch_size': suggested_batch_size,
            'learning_rate': 1e-3
        }
    
    def __repr__(self) -> str:
        """Enhanced string representation of the BRITS model."""
        status = "fitted" if self.is_fitted else "unfitted"
        return (f"BRITSModel("
                f"n_features={self.n_features}, "
                f"sequence_len={self.sequence_len}, "
                f"rnn_hidden_size={self.rnn_hidden_size}, "
                f"status={status})")

# Utility functions for BRITS model
def create_brits_model_from_config(config: Dict[str, Any]) -> BRITSModel:
    """
    Create a BRITS model from configuration dictionary.
    
    Args:
        config: Configuration dictionary with model parameters
        
    Returns:
        Configured BRITSModel instance
    """
    return BRITSModel(**config)

def validate_brits_config(config: Dict[str, Any]) -> bool:
    """
    Validate BRITS model configuration.
    
    Args:
        config: Configuration dictionary to validate
        
    Returns:
        bool: True if configuration is valid
    """
    required_keys = ['n_features', 'sequence_len']
    for key in required_keys:
        if key not in config:
            print(f"❌ Missing required configuration key: {key}")
            return False
    
    # Validate RNN hidden size
    rnn_hidden_size = config.get('rnn_hidden_size', 128)
    if rnn_hidden_size < 16 or rnn_hidden_size > 1024:
        print(f"⚠️ Warning: rnn_hidden_size={rnn_hidden_size} may not be optimal (recommended: 16-1024)")
    
    print("✅ BRITS configuration is valid")
    return True
