#!/usr/bin/env python3
"""
Simple test to check TransformerModel method availability
"""

import sys
import importlib

def test_method_availability():
    """Test if the method is available after reloading the module."""
    try:
        # Clear any cached modules
        if 'models.advanced_models.transformer_model' in sys.modules:
            del sys.modules['models.advanced_models.transformer_model']
        if 'models.advanced_models' in sys.modules:
            del sys.modules['models.advanced_models']
        
        # Import fresh
        from models.advanced_models.transformer_model import TransformerModel
        
        print("🔍 Checking TransformerModel methods...")
        
        # Get all methods
        all_methods = dir(TransformerModel)
        estimate_methods = [m for m in all_methods if 'estimate' in m.lower()]
        param_methods = [m for m in all_methods if 'param' in m.lower()]
        
        print(f"   - Total methods: {len(all_methods)}")
        print(f"   - Methods with 'estimate': {estimate_methods}")
        print(f"   - Methods with 'param': {param_methods}")
        
        # Check specifically for _estimate_parameters
        if hasattr(TransformerModel, '_estimate_parameters'):
            print("✅ _estimate_parameters method found!")
            method = getattr(TransformerModel, '_estimate_parameters')
            print(f"   - Method object: {method}")
            print(f"   - Method type: {type(method)}")
            
            # Try to call it on a dummy instance
            try:
                # Create a minimal instance for testing
                dummy = TransformerModel.__new__(TransformerModel)
                dummy.model = None
                dummy.n_encoder_layers = 6
                dummy.d_model = 256
                dummy.d_ff = 1024
                dummy.n_features = 5
                dummy.max_seq_len = 512
                
                result = dummy._estimate_parameters()
                print(f"   - Method call result: {result:,}")
                
            except Exception as e:
                print(f"   - Method call failed: {e}")
        else:
            print("❌ _estimate_parameters method NOT found!")
            
            # Show methods that are similar
            similar_methods = [m for m in all_methods if 'estimate' in m.lower() or 'param' in m.lower() or '_' in m]
            print(f"   - Similar methods: {similar_methods[:10]}")  # Show first 10
        
        return hasattr(TransformerModel, '_estimate_parameters')
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_method_availability()
    print(f"\n{'✅ SUCCESS' if success else '❌ FAILED'}: Method availability test")
