#!/usr/bin/env python3
"""
Simple test script to verify the verbose AttributeError fix in mRNN model.
This bypasses the memory optimizer to focus on the verbose attribute fix.
"""

import torch
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mrnn_verbose_attribute():
    """Test that MRNNModel has verbose attribute and can be initialized."""
    print("Testing mRNN verbose attribute...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Test with verbose=True
        model = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=2,
            batch_size=16,
            verbose=True
        )
        
        # Verify verbose attribute exists and is set correctly
        assert hasattr(model, 'verbose'), "verbose attribute missing"
        assert model.verbose == True, "verbose attribute not set correctly"
        print("SUCCESS: verbose attribute present and set to True")
        
        # Test with verbose=False
        model2 = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=2,
            batch_size=16,
            verbose=False
        )
        
        assert hasattr(model2, 'verbose'), "verbose attribute missing in second model"
        assert model2.verbose == False, "verbose attribute not set correctly in second model"
        print("SUCCESS: verbose attribute present and set to False")
        
        return True
        
    except AttributeError as e:
        if 'verbose' in str(e):
            print(f"FAILED: verbose AttributeError still present: {e}")
            return False
        else:
            print(f"Different AttributeError: {e}")
            raise
    except Exception as e:
        print(f"Test failed with error: {e}")
        raise

def test_mrnn_basic_training():
    """Test basic training without memory optimizer to isolate verbose fix."""
    print("\nTesting basic mRNN training...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Initialize model
        model = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=1,  # Minimal epochs
            batch_size=8,  # Small batch
            verbose=True
        )
        
        # Create minimal test dataset
        n_samples = 20  # Very small dataset
        sequence_len = 64
        n_features = 5
        
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Create training data with missing values
        train_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        missing_mask = torch.rand(n_samples, sequence_len, n_features) < 0.1  # Only 10% missing
        train_data[missing_mask] = float('nan')
        
        # Create ground truth
        truth_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        
        print(f"Test dataset: {train_data.shape}, Missing: {torch.isnan(train_data).sum().item()}")
        
        # Test model initialization directly
        model._initialize_model()
        print("SUCCESS: Model initialized without errors")
        
        # Test that verbose access works in fit method by checking the line that was causing the error
        # Line 660: if self.verbose and (epoch + 1) % 5 == 0:
        verbose_check = model.verbose and (1) % 5 == 0
        print(f"SUCCESS: verbose attribute accessible in training logic: {verbose_check}")
        
        return True
        
    except AttributeError as e:
        if 'verbose' in str(e):
            print(f"FAILED: verbose AttributeError still present: {e}")
            return False
        else:
            print(f"Different AttributeError: {e}")
            raise
    except Exception as e:
        print(f"Basic training test failed: {e}")
        import traceback
        traceback.print_exc()
        raise

if __name__ == "__main__":
    print("Simple mRNN verbose AttributeError Fix Test")
    print("=" * 50)
    
    try:
        success1 = test_mrnn_verbose_attribute()
        success2 = test_mrnn_basic_training()
        
        if success1 and success2:
            print("\n" + "=" * 50)
            print("ALL TESTS PASSED!")
            print("SUCCESS: verbose AttributeError has been fixed")
            print("SUCCESS: mRNN model can access self.verbose without errors")
        else:
            print("\n" + "=" * 50)
            print("TESTS FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        sys.exit(1)