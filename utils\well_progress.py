"""
Well Processing Progress Display Utilities
Provides clean progress bars and improved logging for well processing operations.
"""

import sys
from typing import List, Optional, Any
from tqdm import tqdm
import warnings


class WellProgressTracker:
    """
    Clean progress tracking for well processing operations.
    Replaces verbose per-well messages with progress bars and summary statistics.
    """
    
    def __init__(self, wells: List[str], description: str = "Processing wells", 
                 show_current_well: bool = True, suppress_warnings: bool = False):
        """
        Initialize well progress tracker.
        
        Args:
            wells: List of well names to process
            description: Description for the progress bar
            show_current_well: Whether to show current well name in progress bar
            suppress_warnings: Whether to suppress non-critical warnings during processing
        """
        self.wells = wells
        self.total_wells = len(wells)
        self.description = description
        self.show_current_well = show_current_well
        self.suppress_warnings = suppress_warnings
        
        # Progress tracking
        self.current_well_idx = 0
        self.current_well = None
        self.progress_bar = None
        
        # Statistics tracking
        self.well_stats = {}
        self.total_intervals = 0
        self.total_sequences = 0
        self.failed_wells = []
        self.warnings_captured = []
        
        # Initialize progress bar
        self._init_progress_bar()
        
        # Capture warnings if requested
        if self.suppress_warnings:
            self._setup_warning_capture()
    
    def _init_progress_bar(self):
        """Initialize the progress bar."""
        self.progress_bar = tqdm(
            total=self.total_wells,
            desc=self.description,
            unit="well",
            ncols=100,
            bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} wells [{elapsed}<{remaining}, {rate_fmt}]'
        )
    
    def _setup_warning_capture(self):
        """Setup warning capture to prevent cluttering output."""
        def warning_handler(message, category, filename, lineno, file=None, line=None):
            self.warnings_captured.append({
                'message': str(message),
                'category': category.__name__,
                'filename': filename,
                'lineno': lineno
            })
        
        warnings.showwarning = warning_handler
    
    def start_well(self, well_name: str) -> None:
        """
        Start processing a well.
        
        Args:
            well_name: Name of the well being processed
        """
        self.current_well = well_name
        self.current_well_idx = self.wells.index(well_name) if well_name in self.wells else self.current_well_idx
        
        # Update progress bar description
        if self.show_current_well:
            desc = f"{self.description} | Current: {well_name}"
            self.progress_bar.set_description(desc)
    
    def finish_well(self, well_name: str, intervals: int = 0, sequences: int = 0, 
                   success: bool = True, error_msg: str = None) -> None:
        """
        Finish processing a well and record statistics.
        
        Args:
            well_name: Name of the well that was processed
            intervals: Number of valid intervals found
            sequences: Number of sequences created
            success: Whether processing was successful
            error_msg: Error message if processing failed
        """
        # Record statistics
        self.well_stats[well_name] = {
            'intervals': intervals,
            'sequences': sequences,
            'success': success,
            'error': error_msg
        }
        
        # Update totals
        if success:
            self.total_intervals += intervals
            self.total_sequences += sequences
        else:
            self.failed_wells.append({'well': well_name, 'error': error_msg})
        
        # Update progress bar
        self.progress_bar.update(1)
        
        # Update postfix with current statistics
        self._update_progress_postfix()
    
    def _update_progress_postfix(self):
        """Update progress bar postfix with current statistics."""
        successful_wells = sum(1 for stats in self.well_stats.values() if stats['success'])
        postfix = f"✓{successful_wells}"
        
        if self.failed_wells:
            postfix += f" ✗{len(self.failed_wells)}"
        
        if self.total_sequences > 0:
            postfix += f" | {self.total_sequences:,} seq"
        
        self.progress_bar.set_postfix_str(postfix)
    
    def log_error(self, well_name: str, error_msg: str) -> None:
        """
        Log an error for a specific well.
        
        Args:
            well_name: Name of the well with error
            error_msg: Error message
        """
        # Print error immediately (these are important)
        tqdm.write(f"❌ Error in well '{well_name}': {error_msg}")
    
    def log_warning(self, well_name: str, warning_msg: str) -> None:
        """
        Log a warning for a specific well.
        
        Args:
            well_name: Name of the well with warning
            warning_msg: Warning message
        """
        if not self.suppress_warnings:
            tqdm.write(f"⚠️ Warning in well '{well_name}': {warning_msg}")
    
    def close(self) -> None:
        """Close the progress bar and print summary."""
        if self.progress_bar:
            self.progress_bar.close()
        
        self._print_summary()
    
    def _print_summary(self) -> None:
        """Print processing summary."""
        successful_wells = sum(1 for stats in self.well_stats.values() if stats['success'])
        
        print(f"\n📊 Well Processing Summary:")
        print(f"   • Total wells processed: {len(self.well_stats)}")
        print(f"   • Successful: {successful_wells}")
        print(f"   • Failed: {len(self.failed_wells)}")
        print(f"   • Total valid intervals: {self.total_intervals:,}")
        print(f"   • Total sequences created: {self.total_sequences:,}")
        
        # Show failed wells if any
        if self.failed_wells:
            print(f"\n❌ Failed wells:")
            for failed in self.failed_wells[:5]:  # Show first 5 failures
                print(f"   • {failed['well']}: {failed['error']}")
            if len(self.failed_wells) > 5:
                print(f"   • ... and {len(self.failed_wells) - 5} more")
        
        # Show captured warnings summary
        if self.suppress_warnings and self.warnings_captured:
            warning_types = {}
            for warning in self.warnings_captured:
                category = warning['category']
                warning_types[category] = warning_types.get(category, 0) + 1
            
            print(f"\n⚠️ Warnings captured (suppressed): {len(self.warnings_captured)} total")
            for category, count in warning_types.items():
                print(f"   • {category}: {count}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()


def create_well_progress_tracker(wells: List[str], description: str = "Processing wells",
                                show_current_well: bool = True, 
                                suppress_warnings: bool = True) -> WellProgressTracker:
    """
    Create a well progress tracker with sensible defaults.
    
    Args:
        wells: List of well names to process
        description: Description for the progress bar
        show_current_well: Whether to show current well name in progress bar
        suppress_warnings: Whether to suppress non-critical warnings
        
    Returns:
        WellProgressTracker instance
    """
    return WellProgressTracker(
        wells=wells,
        description=description,
        show_current_well=show_current_well,
        suppress_warnings=suppress_warnings
    )
