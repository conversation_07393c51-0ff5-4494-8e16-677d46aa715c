"""
Fix for autoencoder evaluation issues.
This script provides improved evaluation logic to prevent the "Could not evaluate model performance" warning.
"""

import numpy as np
import pandas as pd
import torch
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

def improved_autoencoder_evaluation(model, val_df_scaled, val_truth_tensor, target_col, all_features, hparams, use_enhanced_preprocessing=True):
    """
    Improved evaluation function that handles edge cases and provides better error reporting.
    
    Args:
        model: Trained autoencoder model
        val_df_scaled: Scaled validation dataframe
        val_truth_tensor: Ground truth tensor
        target_col: Target column name
        all_features: List of all feature names
        hparams: Hyperparameters dictionary
        use_enhanced_preprocessing: Whether to use enhanced preprocessing
        
    Returns:
        dict: Evaluation results with detailed status information
    """
    from ml_core import create_sequences
    
    print("🔧 IMPROVED AUTOENCODER EVALUATION")
    print("=" * 50)
    
    # Initialize with more descriptive status
    evaluation_results = {
        'imputation_metrics': {'mae': None, 'r2': None, 'rmse': None, 'status': 'not_attempted', 'error': None},
        'prediction_metrics': {'mae': None, 'r2': None, 'rmse': None, 'status': 'not_attempted', 'error': None},
        'evaluation_successful': False,
        'warning_triggered': False,
        'recommendations': []
    }
    
    target_idx = all_features.index(target_col)
    
    # === IMPUTATION EVALUATION ===
    print("\n1. IMPUTATION EVALUATION:")
    try:
        evaluation_results['imputation_metrics']['status'] = 'attempting'
        
        # Create validation sequences
        val_train_sequences, _ = create_sequences(val_df_scaled, 'WELL', all_features,
                                                 sequence_len=hparams['sequence_len'],
                                                 use_enhanced=use_enhanced_preprocessing)
        
        if val_train_sequences.shape[0] == 0:
            raise ValueError("No validation sequences created")
        
        val_train_tensor = torch.from_numpy(val_train_sequences.astype(np.float32))
        
        # Get model predictions
        if hasattr(model, 'predict_large_dataset'):
            imputed_val_tensor = model.predict_large_dataset(val_train_tensor)
        else:
            imputed_val_tensor = model.predict(val_train_tensor)
        
        # Check for artificially missing values
        val_mask = torch.isnan(val_train_tensor[:, :, target_idx])
        
        if not val_mask.any():
            # If no missing values, create some artificially for evaluation
            print("   ⚠️ No missing values found, creating artificial missing values...")
            
            # Randomly mask 10% of values for evaluation
            mask_ratio = 0.1
            total_elements = val_train_tensor[:, :, target_idx].numel()
            num_to_mask = int(total_elements * mask_ratio)
            
            # Create random mask
            flat_indices = torch.randperm(total_elements)[:num_to_mask]
            val_mask_artificial = torch.zeros_like(val_train_tensor[:, :, target_idx], dtype=torch.bool)
            val_mask_artificial.view(-1)[flat_indices] = True
            
            # Apply mask to create missing values
            val_train_tensor_masked = val_train_tensor.clone()
            val_train_tensor_masked[:, :, target_idx][val_mask_artificial] = float('nan')
            
            # Re-predict with masked data
            if hasattr(model, 'predict_large_dataset'):
                imputed_val_tensor = model.predict_large_dataset(val_train_tensor_masked)
            else:
                imputed_val_tensor = model.predict(val_train_tensor_masked)
            
            val_mask = val_mask_artificial
        
        # Extract predictions and ground truth for missing values
        y_pred_val = imputed_val_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()
        y_true_val = val_truth_tensor[:, :, target_idx][val_mask].detach().cpu().numpy()
        
        # Filter valid data
        valid_indices = ~np.isnan(y_true_val) & ~np.isnan(y_pred_val) & np.isfinite(y_true_val) & np.isfinite(y_pred_val)
        y_pred_val = y_pred_val[valid_indices]
        y_true_val = y_true_val[valid_indices]
        
        if len(y_true_val) < 10:  # Minimum threshold for reliable evaluation
            raise ValueError(f"Insufficient valid data points for evaluation: {len(y_true_val)}")
        
        # Calculate metrics
        evaluation_results['imputation_metrics']['mae'] = mean_absolute_error(y_true_val, y_pred_val)
        evaluation_results['imputation_metrics']['r2'] = r2_score(y_true_val, y_pred_val)
        evaluation_results['imputation_metrics']['rmse'] = np.sqrt(mean_squared_error(y_true_val, y_pred_val))
        evaluation_results['imputation_metrics']['status'] = 'success'
        
        print(f"   ✅ Imputation evaluation successful:")
        print(f"      • MAE: {evaluation_results['imputation_metrics']['mae']:.4f}")
        print(f"      • R²: {evaluation_results['imputation_metrics']['r2']:.4f}")
        print(f"      • RMSE: {evaluation_results['imputation_metrics']['rmse']:.4f}")
        print(f"      • Evaluated points: {len(y_true_val)}")
        
    except Exception as e:
        evaluation_results['imputation_metrics']['status'] = 'failed'
        evaluation_results['imputation_metrics']['error'] = str(e)
        print(f"   ❌ Imputation evaluation failed: {e}")
        evaluation_results['recommendations'].append("Check validation data quality and sequence creation parameters")
    
    # === PREDICTION EVALUATION (IMPROVED) ===
    print("\n2. PREDICTION EVALUATION (IMPROVED):")
    try:
        evaluation_results['prediction_metrics']['status'] = 'attempting'
        
        # Strategy 1: Try with entirely masked target
        print("   Attempting Strategy 1: Entirely masked target...")
        
        val_df_pred = val_df_scaled.copy()
        val_df_pred[target_col] = np.nan
        
        val_pred_sequences, _ = create_sequences(val_df_pred, 'WELL', all_features,
                                               sequence_len=hparams['sequence_len'],
                                               use_enhanced=use_enhanced_preprocessing)
        
        if val_pred_sequences.shape[0] > 0:
            val_pred_tensor = torch.from_numpy(val_pred_sequences.astype(np.float32))
            
            # Get predictions
            if hasattr(model, 'predict_large_dataset'):
                pred_output = model.predict_large_dataset(val_pred_tensor)
            else:
                pred_output = model.predict(val_pred_tensor)
            
            # Compare with ground truth
            y_pred_full = pred_output[:, :, target_idx].detach().cpu().numpy().flatten()
            y_true_full = val_truth_tensor[:, :, target_idx].detach().cpu().numpy().flatten()
            
            # Filter valid data
            valid_idx = ~np.isnan(y_true_full) & ~np.isnan(y_pred_full) & np.isfinite(y_true_full) & np.isfinite(y_pred_full)
            
            if valid_idx.sum() >= 10:  # Minimum threshold
                y_pred_filtered = y_pred_full[valid_idx]
                y_true_filtered = y_true_full[valid_idx]
                
                evaluation_results['prediction_metrics']['mae'] = mean_absolute_error(y_true_filtered, y_pred_filtered)
                evaluation_results['prediction_metrics']['r2'] = r2_score(y_true_filtered, y_pred_filtered)
                evaluation_results['prediction_metrics']['rmse'] = np.sqrt(mean_squared_error(y_true_filtered, y_pred_filtered))
                evaluation_results['prediction_metrics']['status'] = 'success'
                
                print(f"   ✅ Strategy 1 successful:")
                print(f"      • MAE: {evaluation_results['prediction_metrics']['mae']:.4f}")
                print(f"      • R²: {evaluation_results['prediction_metrics']['r2']:.4f}")
                print(f"      • RMSE: {evaluation_results['prediction_metrics']['rmse']:.4f}")
                print(f"      • Evaluated points: {valid_idx.sum()}")
            else:
                raise ValueError(f"Strategy 1 failed: insufficient valid points ({valid_idx.sum()})")
        else:
            raise ValueError("Strategy 1 failed: no sequences created with entirely masked target")
            
    except Exception as e1:
        print(f"   ⚠️ Strategy 1 failed: {e1}")
        
        # Strategy 2: Use cross-validation approach
        print("   Attempting Strategy 2: Cross-validation approach...")
        try:
            # Split validation data into chunks and predict each chunk using others as context
            val_sequences_orig, _ = create_sequences(val_df_scaled, 'WELL', all_features,
                                                   sequence_len=hparams['sequence_len'],
                                                   use_enhanced=use_enhanced_preprocessing)
            
            if val_sequences_orig.shape[0] > 0:
                val_tensor_orig = torch.from_numpy(val_sequences_orig.astype(np.float32))
                
                # Use every 5th sequence for prediction evaluation
                test_indices = list(range(0, val_tensor_orig.shape[0], 5))
                
                if len(test_indices) >= 10:
                    val_test_tensor = val_tensor_orig[test_indices]
                    
                    # Mask target in test sequences
                    val_test_masked = val_test_tensor.clone()
                    val_test_masked[:, :, target_idx] = float('nan')
                    
                    # Get predictions
                    if hasattr(model, 'predict_large_dataset'):
                        pred_output = model.predict_large_dataset(val_test_masked)
                    else:
                        pred_output = model.predict(val_test_masked)
                    
                    # Extract predictions and ground truth
                    y_pred = pred_output[:, :, target_idx].detach().cpu().numpy().flatten()
                    y_true = val_test_tensor[:, :, target_idx].detach().cpu().numpy().flatten()
                    
                    # Filter valid data
                    valid_idx = ~np.isnan(y_true) & ~np.isnan(y_pred) & np.isfinite(y_true) & np.isfinite(y_pred)
                    
                    if valid_idx.sum() >= 10:
                        y_pred_filtered = y_pred[valid_idx]
                        y_true_filtered = y_true[valid_idx]
                        
                        evaluation_results['prediction_metrics']['mae'] = mean_absolute_error(y_true_filtered, y_pred_filtered)
                        evaluation_results['prediction_metrics']['r2'] = r2_score(y_true_filtered, y_pred_filtered)
                        evaluation_results['prediction_metrics']['rmse'] = np.sqrt(mean_squared_error(y_true_filtered, y_pred_filtered))
                        evaluation_results['prediction_metrics']['status'] = 'success_cv'
                        
                        print(f"   ✅ Strategy 2 successful:")
                        print(f"      • MAE: {evaluation_results['prediction_metrics']['mae']:.4f}")
                        print(f"      • R²: {evaluation_results['prediction_metrics']['r2']:.4f}")
                        print(f"      • RMSE: {evaluation_results['prediction_metrics']['rmse']:.4f}")
                        print(f"      • Evaluated points: {valid_idx.sum()}")
                    else:
                        raise ValueError(f"Strategy 2 failed: insufficient valid points ({valid_idx.sum()})")
                else:
                    raise ValueError("Strategy 2 failed: insufficient test sequences")
            else:
                raise ValueError("Strategy 2 failed: no original sequences available")
                
        except Exception as e2:
            evaluation_results['prediction_metrics']['status'] = 'failed'
            evaluation_results['prediction_metrics']['error'] = f"Both strategies failed: {e1}; {e2}"
            print(f"   ❌ Strategy 2 also failed: {e2}")
            evaluation_results['recommendations'].append("Model may not be suitable for prediction tasks - consider using only for imputation")
    
    # === FINAL ANALYSIS ===
    print("\n3. FINAL ANALYSIS:")
    
    imp_success = evaluation_results['imputation_metrics']['status'] == 'success'
    pred_success = evaluation_results['prediction_metrics']['status'] in ['success', 'success_cv']
    
    evaluation_results['evaluation_successful'] = imp_success and pred_success
    
    # Check if warning would be triggered
    imp_r2 = evaluation_results['imputation_metrics']['r2']
    pred_r2 = evaluation_results['prediction_metrics']['r2']
    
    warning_condition = not (imp_r2 is not None and imp_r2 > 0 and pred_r2 is not None and pred_r2 > 0)
    evaluation_results['warning_triggered'] = warning_condition
    
    if warning_condition:
        print("   ❌ WARNING WOULD BE TRIGGERED")
        evaluation_results['recommendations'].append("Evaluation failed - check data quality and model compatibility")
    else:
        print("   ✅ EVALUATION SUCCESSFUL - NO WARNING")
        
        # Calculate performance metrics
        r2_drop = imp_r2 - pred_r2
        performance_ratio = pred_r2 / imp_r2 if imp_r2 > 0 else 0
        
        print(f"   • R² Drop: {r2_drop:.4f}")
        print(f"   • Performance Ratio: {performance_ratio:.2%}")
        
        evaluation_results['r2_drop'] = r2_drop
        evaluation_results['performance_ratio'] = performance_ratio
        
        # Add interpretation
        if abs(r2_drop) > 0.2:
            evaluation_results['recommendations'].append("Significant performance difference between imputation and prediction tasks")
        if performance_ratio < 0.8:
            evaluation_results['recommendations'].append("Consider using separate models for prediction tasks")
    
    print("=" * 50)
    return evaluation_results

if __name__ == "__main__":
    print("This is an improved evaluation utility. Import and use improved_autoencoder_evaluation().")
