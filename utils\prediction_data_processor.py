"""
Prediction Data Processor for Prediction-Only Transformer

This module provides specialized data processing utilities for the prediction-only transformer mode.
It handles valid data extraction, variable-length sequence batching, and prediction sample creation.

Key Features:
- Extract valid (finite) data points from sequences with missing values
- Create prediction samples with context and target pairs
- Handle variable-length sequences efficiently
- Provide custom collate functions for DataLoader
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import warnings


class PredictionDataProcessor:
    """
    Data processor for prediction-only transformer training and inference.
    
    Handles extraction of valid data points and creation of prediction samples
    that eliminate missing values from the computation graph.
    """
    
    def __init__(self, min_context_length: int = 2, max_context_length: int = 32,
                 missing_value_token: float = -999.0):
        """
        Initialize the prediction data processor.
        
        Args:
            min_context_length: Minimum number of valid points needed for context
            max_context_length: Maximum context length to prevent memory issues
            missing_value_token: Token used to represent missing values
        """
        self.min_context_length = min_context_length
        self.max_context_length = max_context_length
        self.missing_value_token = missing_value_token
        
    def process_sequences(self, sequences: torch.Tensor, 
                         targets: Optional[torch.Tensor] = None) -> List[Dict[str, Any]]:
        """
        Process sequences to extract valid prediction samples.
        
        Args:
            sequences: Input sequences [batch_size, seq_len, n_features] with potential missing values
            targets: Target sequences [batch_size, seq_len, n_features] (optional, for training)
            
        Returns:
            List of prediction samples with contexts and targets
        """
        prediction_samples = []
        
        for seq_idx, sequence in enumerate(sequences):
            # Find positions with all finite values across all features
            valid_mask = self._get_valid_mask(sequence)
            valid_positions = torch.where(valid_mask)[0]
            
            if len(valid_positions) < self.min_context_length + 1:
                # Need at least min_context_length + 1 points (context + target)
                continue
                
            valid_values = sequence[valid_positions]  # [n_valid, n_features]
            
            # Create prediction samples from this sequence
            samples = self._create_prediction_samples(
                valid_values, valid_positions, seq_idx, 
                targets[seq_idx] if targets is not None else None
            )
            prediction_samples.extend(samples)
            
        return prediction_samples
    
    def _get_valid_mask(self, sequence: torch.Tensor) -> torch.Tensor:
        """
        Get mask indicating positions with all finite values.
        
        Args:
            sequence: Input sequence [seq_len, n_features]
            
        Returns:
            Boolean mask [seq_len] indicating valid positions
        """
        # Check for finite values (not NaN, not inf, not missing_value_token)
        finite_mask = torch.isfinite(sequence)
        not_missing_mask = sequence != self.missing_value_token
        
        # All features must be valid at a position
        valid_mask = (finite_mask & not_missing_mask).all(dim=-1)
        
        return valid_mask
    
    def _create_prediction_samples(self, valid_values: torch.Tensor, 
                                  valid_positions: torch.Tensor, seq_idx: int,
                                  target_sequence: Optional[torch.Tensor] = None) -> List[Dict[str, Any]]:
        """
        Create prediction samples from valid values.
        
        Args:
            valid_values: Valid data points [n_valid, n_features]
            valid_positions: Original positions of valid points [n_valid]
            seq_idx: Sequence index in the batch
            target_sequence: Target sequence for training (optional)
            
        Returns:
            List of prediction samples
        """
        samples = []
        n_valid = len(valid_values)
        
        # Create multiple prediction samples from this sequence
        for target_idx in range(self.min_context_length, n_valid):
            # Context: all valid points before target
            context_start = max(0, target_idx - self.max_context_length)
            context_values = valid_values[context_start:target_idx]
            context_positions = valid_positions[context_start:target_idx]
            
            # Target: the next valid point
            target_value = valid_values[target_idx]
            target_position = valid_positions[target_idx]
            
            # Create sample
            sample = {
                'context': context_values,  # [context_len, n_features]
                'target': target_value,     # [n_features]
                'context_positions': context_positions,  # [context_len]
                'target_position': target_position,      # scalar
                'sequence_idx': seq_idx,
                'context_length': len(context_values)
            }
            
            # Add ground truth target if available
            if target_sequence is not None:
                sample['ground_truth'] = target_sequence[target_position]
            
            samples.append(sample)
            
        return samples
    
    def create_batches(self, prediction_samples: List[Dict[str, Any]], 
                      batch_size: int = 32) -> List[Dict[str, torch.Tensor]]:
        """
        Create batches from prediction samples with padding for variable lengths.
        
        Args:
            prediction_samples: List of prediction samples
            batch_size: Batch size
            
        Returns:
            List of batched samples
        """
        batches = []
        
        for i in range(0, len(prediction_samples), batch_size):
            batch_samples = prediction_samples[i:i + batch_size]
            batched = self.collate_prediction_samples(batch_samples)
            batches.append(batched)
            
        return batches
    
    def collate_prediction_samples(self, batch_samples: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """
        Collate function for DataLoader to handle variable-length sequences.
        
        Args:
            batch_samples: List of prediction samples in a batch
            
        Returns:
            Batched tensors with appropriate padding
        """
        if not batch_samples:
            return {}
        
        # Find maximum context length in this batch
        max_context_len = max(sample['context_length'] for sample in batch_samples)
        batch_size = len(batch_samples)
        n_features = batch_samples[0]['context'].shape[-1]
        
        # Initialize batched tensors
        contexts = torch.zeros(batch_size, max_context_len, n_features)
        targets = torch.zeros(batch_size, n_features)
        context_masks = torch.zeros(batch_size, max_context_len, dtype=torch.bool)
        positions = torch.zeros(batch_size, max_context_len, dtype=torch.long)
        
        # Fill batched tensors
        for i, sample in enumerate(batch_samples):
            context_len = sample['context_length']
            
            # Context values and positions
            contexts[i, :context_len] = sample['context']
            context_masks[i, :context_len] = True
            positions[i, :context_len] = sample['context_positions']
            
            # Target values
            targets[i] = sample['target']
        
        return {
            'contexts': contexts,
            'targets': targets,
            'masks': context_masks,
            'positions': positions,
            'batch_size': batch_size,
            'max_context_length': max_context_len
        }
    
    def extract_valid_sequences(self, batch_sequences: torch.Tensor) -> List[Dict[str, Any]]:
        """
        Extract valid (finite) data points from batch sequences.
        
        Args:
            batch_sequences: [batch_size, seq_len, input_dim] with potential NaN values
            
        Returns:
            List of dictionaries containing valid sequences and metadata
        """
        batch_results = []
        
        for seq_idx, sequence in enumerate(batch_sequences):
            # Find positions with all finite values across all features
            valid_mask = self._get_valid_mask(sequence)
            valid_positions = torch.where(valid_mask)[0]
            
            if len(valid_positions) >= self.min_context_length:
                valid_values = sequence[valid_positions]  # [n_valid, input_dim]
                
                batch_results.append({
                    'values': valid_values,
                    'positions': valid_positions,
                    'sequence_idx': seq_idx,
                    'original_length': len(sequence),
                    'valid_count': len(valid_positions)
                })
        
        return batch_results
    
    def get_statistics(self, prediction_samples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get statistics about the processed prediction samples.
        
        Args:
            prediction_samples: List of prediction samples
            
        Returns:
            Dictionary with statistics
        """
        if not prediction_samples:
            return {'total_samples': 0}
        
        context_lengths = [sample['context_length'] for sample in prediction_samples]
        
        return {
            'total_samples': len(prediction_samples),
            'avg_context_length': np.mean(context_lengths),
            'min_context_length': np.min(context_lengths),
            'max_context_length': np.max(context_lengths),
            'unique_sequences': len(set(sample['sequence_idx'] for sample in prediction_samples))
        }


def validate_prediction_samples(prediction_samples: List[Dict[str, Any]]) -> bool:
    """
    Validate prediction samples for consistency and correctness.
    
    Args:
        prediction_samples: List of prediction samples to validate
        
    Returns:
        True if all samples are valid, False otherwise
    """
    if not prediction_samples:
        return False
    
    required_keys = ['context', 'target', 'context_positions', 'target_position', 'sequence_idx']
    
    for i, sample in enumerate(prediction_samples):
        # Check required keys
        if not all(key in sample for key in required_keys):
            warnings.warn(f"Sample {i} missing required keys")
            return False
        
        # Check tensor shapes
        context = sample['context']
        target = sample['target']
        
        if context.dim() != 2 or target.dim() != 1:
            warnings.warn(f"Sample {i} has incorrect tensor dimensions")
            return False
        
        if context.shape[-1] != target.shape[-1]:
            warnings.warn(f"Sample {i} has mismatched feature dimensions")
            return False
        
        # Check for finite values
        if not torch.isfinite(context).all() or not torch.isfinite(target).all():
            warnings.warn(f"Sample {i} contains non-finite values")
            return False
    
    return True
