#!/usr/bin/env python3
"""
Test script to verify the CUDA tensor pinning error fix in the optimized mRNN model.
This script tests that DataLoader works correctly with proper device management.
"""

import torch
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_device_management():
    """Test proper device management and tensor placement."""
    print("🧪 Testing device management...")
    
    # Check CUDA availability
    cuda_available = torch.cuda.is_available()
    print(f"   CUDA available: {cuda_available}")
    
    if cuda_available:
        device = torch.device('cuda')
        print(f"   Using device: {device}")
        print(f"   GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    else:
        device = torch.device('cpu')
        print(f"   Using device: {device}")
    
    return device

def test_tensor_pinning():
    """Test tensor pinning behavior with different tensor locations."""
    print("\n🧪 Testing tensor pinning behavior...")
    
    # Create test tensors
    cpu_tensor = torch.randn(100, 10, dtype=torch.float32)
    print(f"   CPU tensor device: {cpu_tensor.device}")
    
    if torch.cuda.is_available():
        gpu_tensor = cpu_tensor.cuda()
        print(f"   GPU tensor device: {gpu_tensor.device}")
        
        # Test DataLoader with CPU tensors (should work with pin_memory=True)
        try:
            from torch.utils.data import DataLoader, TensorDataset
            cpu_dataset = TensorDataset(cpu_tensor)
            cpu_loader = DataLoader(cpu_dataset, batch_size=16, pin_memory=True)
            print("   ✅ CPU tensors with pin_memory=True: SUCCESS")
        except Exception as e:
            print(f"   ❌ CPU tensors with pin_memory=True: FAILED - {e}")
        
        # Test DataLoader with GPU tensors (should fail with pin_memory=True)
        try:
            gpu_dataset = TensorDataset(gpu_tensor)
            gpu_loader = DataLoader(gpu_dataset, batch_size=16, pin_memory=True)
            # Try to iterate (this is where the error occurs)
            for batch in gpu_loader:
                break
            print("   ⚠️ GPU tensors with pin_memory=True: UNEXPECTED SUCCESS")
        except Exception as e:
            if "cannot pin" in str(e).lower():
                print("   ✅ GPU tensors with pin_memory=True: EXPECTED FAILURE (cannot pin)")
            else:
                print(f"   ❌ GPU tensors with pin_memory=True: UNEXPECTED ERROR - {e}")
        
        # Test DataLoader with GPU tensors (should work with pin_memory=False)
        try:
            gpu_dataset = TensorDataset(gpu_tensor)
            gpu_loader = DataLoader(gpu_dataset, batch_size=16, pin_memory=False)
            for batch in gpu_loader:
                break
            print("   ✅ GPU tensors with pin_memory=False: SUCCESS")
        except Exception as e:
            print(f"   ❌ GPU tensors with pin_memory=False: FAILED - {e}")

def test_mrnn_dataloader_fix():
    """Test that the mRNN model DataLoader fix works correctly."""
    print("\n🧪 Testing mRNN DataLoader fix...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Create test model
        model = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=2,  # Small for testing
            batch_size=32,
            disable_checkpoint=False,
            use_missing_embedding=True
        )
        
        print(f"   Model device: {model.device}")
        
        # Create test data (larger dataset to simulate real scenario)
        n_samples = 1000  # Smaller than real 489,630 but enough to test
        sequence_len = 64
        n_features = 5
        
        # Generate synthetic data with missing values
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Create training data with missing values (on CPU initially)
        train_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        
        # Introduce missing values (NaN) - about 25% missing to simulate real data
        missing_mask = torch.rand(n_samples, sequence_len, n_features) < 0.25
        train_data[missing_mask] = float('nan')
        
        # Create ground truth (complete data)
        truth_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        
        print(f"   Test dataset:")
        print(f"     Shape: {train_data.shape}")
        print(f"     Device: {train_data.device}")
        print(f"     Missing values: {torch.isnan(train_data).sum().item()}")
        print(f"     Missing percentage: {torch.isnan(train_data).sum().item() / train_data.numel() * 100:.1f}%")
        
        # Test the optimized training (this should not fail with pinning error)
        print("\n   🚀 Testing optimized training with DataLoader...")
        
        try:
            model.fit(
                train_data=train_data,
                truth_data=truth_data,
                epochs=1,  # Very small for testing
                batch_size=64,
                patience=1,
                validation_split=0.2
            )
            
            print("   ✅ Training completed successfully without CUDA pinning errors!")
            return True
            
        except Exception as e:
            if "cannot pin" in str(e).lower():
                print(f"   ❌ CUDA pinning error still present: {e}")
                return False
            else:
                print(f"   ❌ Different error occurred: {e}")
                raise
                
    except Exception as e:
        print(f"   ❌ Test setup failed: {e}")
        raise

def test_memory_efficiency():
    """Test that the fix maintains memory efficiency."""
    print("\n🧪 Testing memory efficiency...")
    
    if not torch.cuda.is_available():
        print("   ⚠️ CUDA not available, skipping memory efficiency test")
        return True
    
    try:
        # Clear GPU cache
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Create model with optimizations
        model = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=1,
            batch_size=32,
            disable_checkpoint=False,  # Enable checkpointing
            use_missing_embedding=True
        )
        
        # Create small test data
        train_data = torch.randn(200, 64, 5, dtype=torch.float32)
        truth_data = torch.randn(200, 64, 5, dtype=torch.float32)
        
        # Add missing values
        missing_mask = torch.rand_like(train_data) < 0.2
        train_data[missing_mask] = float('nan')
        
        # Test training
        model.fit(train_data, truth_data, epochs=1, batch_size=16)
        
        final_memory = torch.cuda.memory_allocated()
        memory_used = (final_memory - initial_memory) / 1e6  # MB
        
        print(f"   Memory used: {memory_used:.1f} MB")
        print("   ✅ Memory efficiency test completed")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Memory efficiency test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing mRNN CUDA Tensor Pinning Fix")
    print("=" * 50)
    
    try:
        # Run all tests
        device = test_device_management()
        test_tensor_pinning()
        dataloader_success = test_mrnn_dataloader_fix()
        memory_success = test_memory_efficiency()
        
        print("\n" + "=" * 50)
        if dataloader_success and memory_success:
            print("🎉 ALL TESTS PASSED!")
            print("✅ CUDA tensor pinning error has been fixed")
            print("✅ DataLoader works correctly with device management")
            print("✅ Memory optimizations are functioning")
            print("✅ Training completes without pinning errors")
        else:
            print("❌ SOME TESTS FAILED!")
            if not dataloader_success:
                print("❌ DataLoader pinning issue not resolved")
            if not memory_success:
                print("❌ Memory efficiency issues detected")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
