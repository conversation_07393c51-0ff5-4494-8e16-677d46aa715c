"""
Multiple Linear Regression Utilities for Well Log Imputation

This module provides specialized utilities for implementing multiple linear regression (MLR)
in well log imputation workflows. It includes preprocessing, diagnostic tools, and model
wrappers that integrate seamlessly with the existing ML pipeline.

Key Features:
- Automated preprocessing (scaling, outlier detection)
- Multicollinearity detection using Variance Inflation Factor (VIF)
- Linear regression assumption validation
- Diagnostic plotting and statistical analysis
- Robust error handling with graceful fallbacks

Author: ML Log Prediction System
"""

import numpy as np
import pandas as pd
import warnings
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.base import BaseEstimator, RegressorMixin
import scipy.stats as stats

# Optional dependencies with graceful fallbacks
try:
    from statsmodels.stats.outliers_influence import variance_inflation_factor
    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False
    warnings.warn("Statsmodels not available. VIF calculation will be skipped.")

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    warnings.warn("Matplotlib/Seaborn not available. Diagnostic plots will be skipped.")


class MLRPreprocessor:
    """
    Preprocessor for Multiple Linear Regression models.
    
    Handles feature scaling, outlier detection, and multicollinearity analysis
    specifically designed for well log data characteristics.
    """
    
    def __init__(self, 
                 scaling_method: str = 'standard',
                 outlier_threshold: float = 3.0,
                 vif_threshold: float = 10.0,
                 handle_outliers: bool = True,
                 enable_diagnostics: bool = False):
        """
        Initialize MLR preprocessor.
        
        Args:
            scaling_method: Method for feature scaling ('standard', 'robust', 'minmax')
            outlier_threshold: Z-score threshold for outlier detection
            vif_threshold: VIF threshold for multicollinearity detection
            handle_outliers: Whether to remove/cap outliers
            enable_diagnostics: Whether to generate diagnostic output
        """
        self.scaling_method = scaling_method
        self.outlier_threshold = outlier_threshold
        self.vif_threshold = vif_threshold
        self.handle_outliers = handle_outliers
        self.enable_diagnostics = enable_diagnostics
        
        # Initialize scaler based on method
        if scaling_method == 'standard':
            self.scaler = StandardScaler()
        elif scaling_method == 'robust':
            self.scaler = RobustScaler()
        elif scaling_method == 'minmax':
            self.scaler = MinMaxScaler()
        else:
            raise ValueError(f"Unknown scaling method: {scaling_method}")
        
        self.is_fitted = False
        self.feature_names = None
        self.outlier_mask = None
        self.vif_values = None
        self.diagnostics = {}
    
    def fit(self, X: pd.DataFrame, y: pd.Series = None) -> 'MLRPreprocessor':
        """
        Fit the preprocessor to the training data.
        
        Args:
            X: Feature matrix
            y: Target vector (optional, for diagnostics)
            
        Returns:
            Self for method chaining
        """
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        self.feature_names = list(X.columns)
        
        # Handle outliers if requested
        if self.handle_outliers:
            self.outlier_mask = self._detect_outliers(X)
            X_clean = X[~self.outlier_mask]
            if self.enable_diagnostics:
                outlier_count = self.outlier_mask.sum()
                print(f"🔍 MLR Preprocessing: Detected {outlier_count} outliers ({outlier_count/len(X)*100:.1f}%)")
        else:
            X_clean = X
            self.outlier_mask = pd.Series(False, index=X.index)
        
        # Fit scaler
        self.scaler.fit(X_clean)
        
        # Calculate VIF if available
        if STATSMODELS_AVAILABLE and len(X_clean.columns) > 1:
            try:
                X_scaled = pd.DataFrame(
                    self.scaler.transform(X_clean),
                    columns=X_clean.columns,
                    index=X_clean.index
                )
                self.vif_values = self._calculate_vif(X_scaled)
                
                if self.enable_diagnostics:
                    self._print_vif_diagnostics()
                    
            except Exception as e:
                if self.enable_diagnostics:
                    print(f"⚠️ VIF calculation failed: {e}")
                self.vif_values = None
        
        self.is_fitted = True
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform features using fitted preprocessor.
        
        Args:
            X: Feature matrix to transform
            
        Returns:
            Transformed feature matrix
        """
        if not self.is_fitted:
            raise ValueError("Preprocessor must be fitted before transform")
        
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        
        # Apply scaling
        X_scaled = pd.DataFrame(
            self.scaler.transform(X),
            columns=X.columns,
            index=X.index
        )
        
        return X_scaled
    
    def fit_transform(self, X: pd.DataFrame, y: pd.Series = None) -> pd.DataFrame:
        """
        Fit preprocessor and transform data in one step.
        
        Args:
            X: Feature matrix
            y: Target vector (optional)
            
        Returns:
            Transformed feature matrix
        """
        return self.fit(X, y).transform(X)
    
    def _detect_outliers(self, X: pd.DataFrame) -> pd.Series:
        """
        Detect outliers using Z-score method.
        
        Args:
            X: Feature matrix
            
        Returns:
            Boolean mask indicating outliers
        """
        z_scores = np.abs(stats.zscore(X, nan_policy='omit'))
        outlier_mask = (z_scores > self.outlier_threshold).any(axis=1)
        return outlier_mask
    
    def _calculate_vif(self, X: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate Variance Inflation Factor for each feature.
        
        Args:
            X: Scaled feature matrix
            
        Returns:
            Dictionary mapping feature names to VIF values
        """
        vif_data = {}
        
        for i, feature in enumerate(X.columns):
            try:
                vif_value = variance_inflation_factor(X.values, i)
                vif_data[feature] = vif_value
            except Exception as e:
                if self.enable_diagnostics:
                    print(f"⚠️ VIF calculation failed for {feature}: {e}")
                vif_data[feature] = np.nan
        
        return vif_data
    
    def _print_vif_diagnostics(self):
        """Print VIF diagnostic information."""
        if self.vif_values is None:
            return
        
        print("\n📊 Multicollinearity Analysis (VIF):")
        print("   Feature                VIF      Status")
        print("   " + "-" * 40)
        
        for feature, vif in self.vif_values.items():
            if np.isnan(vif):
                status = "Error"
            elif vif > self.vif_threshold:
                status = "High ⚠️"
            elif vif > 5.0:
                status = "Moderate"
            else:
                status = "Low ✓"
            
            print(f"   {feature:<20} {vif:>6.2f}    {status}")
        
        high_vif_features = [f for f, v in self.vif_values.items() 
                           if not np.isnan(v) and v > self.vif_threshold]
        
        if high_vif_features:
            print(f"\n   ⚠️ High multicollinearity detected in: {', '.join(high_vif_features)}")
            print(f"   Consider using Ridge or ElasticNet regression.")
        else:
            print(f"\n   ✅ No severe multicollinearity detected.")


class MLRModelWrapper(BaseEstimator, RegressorMixin):
    """
    Wrapper for sklearn linear regression models with integrated preprocessing.
    
    This wrapper provides a unified interface for different linear regression
    variants while handling preprocessing, diagnostics, and assumption validation.
    """
    
    def __init__(self, 
                 model_type: str = 'linear',
                 scaling_method: str = 'standard',
                 outlier_threshold: float = 3.0,
                 vif_threshold: float = 10.0,
                 handle_outliers: bool = True,
                 enable_diagnostics: bool = False,
                 **model_kwargs):
        """
        Initialize MLR model wrapper.
        
        Args:
            model_type: Type of linear model ('linear', 'ridge', 'lasso', 'elastic_net')
            scaling_method: Feature scaling method
            outlier_threshold: Z-score threshold for outlier detection
            vif_threshold: VIF threshold for multicollinearity
            handle_outliers: Whether to handle outliers
            enable_diagnostics: Whether to generate diagnostics
            **model_kwargs: Additional arguments for the underlying model
        """
        self.model_type = model_type
        self.scaling_method = scaling_method
        self.outlier_threshold = outlier_threshold
        self.vif_threshold = vif_threshold
        self.handle_outliers = handle_outliers
        self.enable_diagnostics = enable_diagnostics
        self.model_kwargs = model_kwargs
        
        # Initialize preprocessor
        self.preprocessor = MLRPreprocessor(
            scaling_method=scaling_method,
            outlier_threshold=outlier_threshold,
            vif_threshold=vif_threshold,
            handle_outliers=handle_outliers,
            enable_diagnostics=enable_diagnostics
        )
        
        # Initialize model based on type
        self.model = self._create_model()
        self.is_fitted = False
        self.feature_names = None
        self.diagnostics = {}
    
    def _create_model(self):
        """Create the appropriate sklearn model based on model_type."""
        if self.model_type == 'linear':
            return LinearRegression(**self.model_kwargs)
        elif self.model_type == 'ridge':
            return Ridge(**self.model_kwargs)
        elif self.model_type == 'lasso':
            return Lasso(**self.model_kwargs)
        elif self.model_type == 'elastic_net':
            return ElasticNet(**self.model_kwargs)
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
    
    def fit(self, X, y):
        """
        Fit the MLR model with preprocessing.
        
        Args:
            X: Feature matrix
            y: Target vector
            
        Returns:
            Self for method chaining
        """
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        y = pd.Series(y) if not isinstance(y, pd.Series) else y
        
        self.feature_names = list(X.columns)
        
        if self.enable_diagnostics:
            print(f"\n🔧 Training {self.model_type.replace('_', ' ').title()} Regression")
            print(f"   Features: {len(X.columns)}, Samples: {len(X)}")
        
        # Fit preprocessor and transform features
        X_processed = self.preprocessor.fit_transform(X, y)
        
        # Remove outliers from both X and y if requested
        if self.handle_outliers and self.preprocessor.outlier_mask is not None:
            clean_mask = ~self.preprocessor.outlier_mask
            X_clean = X_processed[clean_mask]
            y_clean = y[clean_mask]
        else:
            X_clean = X_processed
            y_clean = y
        
        # Fit the model
        try:
            self.model.fit(X_clean, y_clean)
            self.is_fitted = True
            
            if self.enable_diagnostics:
                self._generate_diagnostics(X_clean, y_clean)
                
        except Exception as e:
            print(f"❌ Model fitting failed: {e}")
            raise
        
        return self
    
    def predict(self, X):
        """
        Make predictions using the fitted model.
        
        Args:
            X: Feature matrix
            
        Returns:
            Predicted values
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        X = pd.DataFrame(X) if not isinstance(X, pd.DataFrame) else X
        X_processed = self.preprocessor.transform(X)
        
        return self.model.predict(X_processed)
    
    def get_params(self, deep=True):
        """Get parameters for this estimator."""
        params = {
            'model_type': self.model_type,
            'scaling_method': self.scaling_method,
            'outlier_threshold': self.outlier_threshold,
            'vif_threshold': self.vif_threshold,
            'handle_outliers': self.handle_outliers,
            'enable_diagnostics': self.enable_diagnostics
        }
        
        # Add model-specific parameters
        if deep and hasattr(self.model, 'get_params'):
            model_params = self.model.get_params(deep=True)
            for key, value in model_params.items():
                params[f'model__{key}'] = value
        
        return params
    
    def set_params(self, **params):
        """Set parameters for this estimator."""
        model_params = {}
        wrapper_params = {}
        
        for key, value in params.items():
            if key.startswith('model__'):
                model_params[key[7:]] = value  # Remove 'model__' prefix
            else:
                wrapper_params[key] = value
        
        # Set wrapper parameters
        for key, value in wrapper_params.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        # Set model parameters
        if model_params and hasattr(self.model, 'set_params'):
            self.model.set_params(**model_params)
        
        # Recreate preprocessor if preprocessing parameters changed
        preprocessing_params = ['scaling_method', 'outlier_threshold', 'vif_threshold', 
                              'handle_outliers', 'enable_diagnostics']
        if any(param in wrapper_params for param in preprocessing_params):
            self.preprocessor = MLRPreprocessor(
                scaling_method=self.scaling_method,
                outlier_threshold=self.outlier_threshold,
                vif_threshold=self.vif_threshold,
                handle_outliers=self.handle_outliers,
                enable_diagnostics=self.enable_diagnostics
            )
        
        return self
    
    def _generate_diagnostics(self, X, y):
        """Generate diagnostic information for the fitted model."""
        if not self.enable_diagnostics:
            return

        try:
            # Get predictions and residuals
            y_pred = self.model.predict(X)
            residuals = y - y_pred

            # Basic statistics
            r2 = r2_score(y, y_pred)
            mae = mean_absolute_error(y, y_pred)
            rmse = np.sqrt(mean_squared_error(y, y_pred))

            print(f"\n📈 Model Diagnostics:")
            print(f"   R² Score: {r2:.4f}")
            print(f"   MAE: {mae:.4f}")
            print(f"   RMSE: {rmse:.4f}")

            # Extract and display mathematical equation
            equation_info = self.extract_mathematical_equation()
            if equation_info:
                self._display_mathematical_equation(equation_info, r2, mae, rmse)

            # Feature coefficients (if available)
            if hasattr(self.model, 'coef_'):
                print(f"\n📊 Feature Coefficients:")
                for i, (feature, coef) in enumerate(zip(self.feature_names, self.model.coef_)):
                    print(f"   {feature:<20}: {coef:>8.4f}")

                if hasattr(self.model, 'intercept_'):
                    print(f"   {'Intercept':<20}: {self.model.intercept_:>8.4f}")

            # Residual analysis
            print(f"\n🔍 Residual Analysis:")
            print(f"   Mean residual: {np.mean(residuals):.6f}")
            print(f"   Std residual: {np.std(residuals):.4f}")

            # Normality test for residuals
            try:
                _, p_value = stats.shapiro(residuals[:min(5000, len(residuals))])  # Limit for performance
                print(f"   Normality test p-value: {p_value:.4f}")
                if p_value > 0.05:
                    print(f"   ✅ Residuals appear normally distributed")
                else:
                    print(f"   ⚠️ Residuals may not be normally distributed")
            except Exception:
                print(f"   ⚠️ Could not perform normality test")

        except Exception as e:
            print(f"⚠️ Diagnostic generation failed: {e}")

    def extract_mathematical_equation(self, target_name="TARGET"):
        """
        Extract the mathematical equation from the fitted model.

        Args:
            target_name: Name of the target variable

        Returns:
            Dictionary with equation information or None if not available
        """
        if not self.is_fitted or not hasattr(self.model, 'coef_'):
            return None

        try:
            coefficients = self.model.coef_
            intercept = self.model.intercept_ if hasattr(self.model, 'intercept_') else 0
            feature_names = self.feature_names

            # Create equation components
            equation_parts = []
            formatted_equation = f"{target_name} = "

            # Add coefficient terms
            for i, (feature, coef) in enumerate(zip(feature_names, coefficients)):
                if i == 0:
                    if coef >= 0:
                        formatted_equation += f"{coef:.2f}×{feature}"
                    else:
                        formatted_equation += f"{coef:.2f}×{feature}"
                else:
                    if coef >= 0:
                        formatted_equation += f" + {coef:.2f}×{feature}"
                    else:
                        formatted_equation += f" - {abs(coef):.2f}×{feature}"

                equation_parts.append({
                    'feature': feature,
                    'coefficient': coef,
                    'interpretation': 'positive correlation' if coef > 0 else 'negative correlation'
                })

            # Add intercept
            if intercept >= 0:
                formatted_equation += f" + {intercept:.2f}"
            else:
                formatted_equation += f" - {abs(intercept):.2f}"

            return {
                'equation': formatted_equation,
                'coefficients': coefficients,
                'intercept': intercept,
                'feature_names': feature_names,
                'equation_parts': equation_parts,
                'model_type': self.model_type,
                'interpretability_note': self._get_interpretability_note()
            }

        except Exception as e:
            print(f"⚠️ Equation extraction failed: {e}")
            return None

    def _get_interpretability_note(self):
        """Get interpretability note based on model type."""
        notes = {
            'linear': "Direct interpretation: coefficients represent true linear relationships",
            'ridge': "Ridge regularization: coefficients are shrunk toward zero (biased but stable)",
            'lasso': "Lasso regularization: some coefficients may be zero (feature selection)",
            'elastic_net': "ElasticNet regularization: combines shrinkage and feature selection"
        }
        return notes.get(self.model_type, "Regularized model: coefficients may be biased")

    def _display_mathematical_equation(self, equation_info, r2, mae, rmse):
        """Display the mathematical equation in a formatted way."""
        print(f"\n📐 Mathematical Equation ({equation_info['model_type'].replace('_', ' ').title()}):")
        print(f"   {equation_info['equation']}")

        print(f"\n📊 Model Performance:")
        print(f"   R² = {r2:.3f} | MAE = {mae:.2f} | RMSE = {rmse:.2f}")

        print(f"\n📋 Feature Interpretation:")
        for part in equation_info['equation_parts']:
            coef = part['coefficient']
            feature = part['feature']
            interpretation = part['interpretation']
            print(f"   • {feature}: {coef:+.2f} ({interpretation})")

        print(f"\n💡 Interpretability:")
        print(f"   {equation_info['interpretability_note']}")

        # Add geological context for common well log features
        self._add_geological_context(equation_info['equation_parts'])

    def _add_geological_context(self, equation_parts):
        """Add geological interpretation context for common well log features."""
        geological_context = {
            'GR': 'Gamma Ray - indicates clay content and lithology',
            'NPHI': 'Neutron Porosity - measures formation porosity',
            'RHOB': 'Bulk Density - indicates formation density and porosity',
            'DT': 'Delta Time (Sonic) - relates to porosity and lithology',
            'RT': 'Resistivity - indicates hydrocarbon saturation',
            'SP': 'Spontaneous Potential - indicates permeability and lithology',
            'CALI': 'Caliper - measures borehole diameter',
            'MD': 'Measured Depth - depth trend effects'
        }

        relevant_features = [part['feature'] for part in equation_parts
                           if part['feature'] in geological_context]

        if relevant_features:
            print(f"\n🌍 Geological Context:")
            for part in equation_parts:
                feature = part['feature']
                if feature in geological_context:
                    coef = part['coefficient']
                    context = geological_context[feature]
                    trend = "increases" if coef > 0 else "decreases"
                    print(f"   • {feature}: {context}")
                    print(f"     → Target {trend} by {abs(coef):.2f} units per unit increase in {feature}")

    def get_equation_for_reporting(self, target_name="TARGET"):
        """
        Get equation information formatted for reporting.

        Args:
            target_name: Name of the target variable

        Returns:
            Dictionary with formatted equation information for reports
        """
        equation_info = self.extract_mathematical_equation(target_name)
        if not equation_info:
            return None

        return {
            'model_type': equation_info['model_type'],
            'equation_text': equation_info['equation'],
            'coefficients': {name: coef for name, coef in zip(equation_info['feature_names'], equation_info['coefficients'])},
            'intercept': equation_info['intercept'],
            'interpretability_note': equation_info['interpretability_note'],
            'feature_interpretations': equation_info['equation_parts']
        }


def create_mlr_model(model_type: str, **kwargs) -> MLRModelWrapper:
    """
    Factory function to create MLR model instances.
    
    Args:
        model_type: Type of model ('linear', 'ridge', 'lasso', 'elastic_net')
        **kwargs: Additional parameters for the model
        
    Returns:
        Configured MLRModelWrapper instance
    """
    return MLRModelWrapper(model_type=model_type, **kwargs)


def validate_mlr_assumptions(X: pd.DataFrame, y: pd.Series, 
                           model: MLRModelWrapper = None,
                           enable_plots: bool = False) -> Dict[str, Any]:
    """
    Validate linear regression assumptions.
    
    Args:
        X: Feature matrix
        y: Target vector
        model: Fitted MLR model (optional)
        enable_plots: Whether to generate diagnostic plots
        
    Returns:
        Dictionary with assumption validation results
    """
    results = {
        'linearity': {'status': 'unknown', 'details': {}},
        'independence': {'status': 'unknown', 'details': {}},
        'homoscedasticity': {'status': 'unknown', 'details': {}},
        'normality': {'status': 'unknown', 'details': {}},
        'multicollinearity': {'status': 'unknown', 'details': {}}
    }
    
    try:
        # If model is provided, use it for residual analysis
        if model is not None and model.is_fitted:
            y_pred = model.predict(X)
            residuals = y - y_pred
            
            # Homoscedasticity test (Breusch-Pagan test would be ideal)
            # For now, use simple variance analysis
            residual_std = np.std(residuals)
            results['homoscedasticity']['details']['residual_std'] = residual_std
            
            # Normality test for residuals
            if len(residuals) > 3:
                _, p_value = stats.shapiro(residuals[:min(5000, len(residuals))])
                results['normality']['status'] = 'pass' if p_value > 0.05 else 'fail'
                results['normality']['details']['shapiro_p_value'] = p_value
        
        # Multicollinearity check
        if STATSMODELS_AVAILABLE and len(X.columns) > 1:
            preprocessor = MLRPreprocessor(enable_diagnostics=False)
            X_scaled = preprocessor.fit_transform(X)
            vif_values = preprocessor._calculate_vif(X_scaled)
            
            max_vif = max([v for v in vif_values.values() if not np.isnan(v)], default=0)
            results['multicollinearity']['status'] = 'pass' if max_vif < 10 else 'fail'
            results['multicollinearity']['details']['vif_values'] = vif_values
            results['multicollinearity']['details']['max_vif'] = max_vif
        
        return results
        
    except Exception as e:
        print(f"⚠️ Assumption validation failed: {e}")
        return results
