"""
TEFN (Time-series Enhanced Forecasting Network) Visualization Fix
This script demonstrates a robust visualization system for TEFN model predictions
with proper dimension handling and error checking.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Optional, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_and_align_dimensions(
    time_indices: np.ndarray, 
    actual_values: np.ndarray, 
    predicted_values: np.ndarray
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Validate and align dimensions of time series data for plotting.
    
    Args:
        time_indices: Array of time points
        actual_values: Array of actual values
        predicted_values: Array of predicted values
        
    Returns:
        Tuple of aligned arrays (time_indices, actual_values, predicted_values)
        
    Raises:
        ValueError: If arrays cannot be aligned
    """
    logger.info(f"Input shapes - Time: {time_indices.shape}, Actual: {actual_values.shape}, Predicted: {predicted_values.shape}")
    
    # Find the minimum length to align all arrays
    min_length = min(len(time_indices), len(actual_values), len(predicted_values))
    
    # Slice all arrays to the same length
    time_indices_aligned = time_indices[:min_length]
    actual_values_aligned = actual_values[:min_length]
    predicted_values_aligned = predicted_values[:min_length]
    
    logger.info(f"Aligned shapes - Time: {time_indices_aligned.shape}, Actual: {actual_values_aligned.shape}, Predicted: {predicted_values_aligned.shape}")
    
    return time_indices_aligned, actual_values_aligned, predicted_values_aligned

def plot_tefn_forecast(
    time_indices: np.ndarray,
    actual_values: np.ndarray,
    predicted_values: np.ndarray,
    confidence_intervals: Optional[Tuple[np.ndarray, np.ndarray]] = None,
    title: str = "TEFN Time Series Forecast"
) -> None:
    """
    Create comprehensive TEFN forecast visualization with proper dimension handling.
    
    Args:
        time_indices: Array of time points
        actual_values: Array of actual values
        predicted_values: Array of predicted values
        confidence_intervals: Optional tuple of (lower_bound, upper_bound) arrays
        title: Plot title
    """
    try:
        # Validate and align dimensions
        time_indices, actual_values, predicted_values = validate_and_align_dimensions(
            time_indices, actual_values, predicted_values
        )
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(title, fontsize=16)
        
        # Plot 1: Actual vs Predicted
        axes[0, 0].plot(time_indices, actual_values, label='Actual', linewidth=2, color='blue')
        axes[0, 0].plot(time_indices, predicted_values, label='Predicted', linewidth=2, color='red', linestyle='--')
        axes[0, 0].set_title('Actual vs Predicted Values')
        axes[0, 0].set_xlabel('Time')
        axes[0, 0].set_ylabel('Values')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Plot 2: Residuals
        residuals = actual_values - predicted_values
        axes[0, 1].plot(time_indices, residuals, color='green', marker='o', linestyle='', alpha=0.7)
        axes[0, 1].axhline(y=0, color='red', linestyle='--', linewidth=1)
        axes[0, 1].set_title('Residuals Analysis')
        axes[0, 1].set_xlabel('Time')
        axes[0, 1].set_ylabel('Residuals (Actual - Predicted)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Plot 3: Residuals Histogram
        axes[1, 0].hist(residuals, bins=20, color='green', alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('Residuals Distribution')
        axes[1, 0].set_xlabel('Residual Values')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Plot 4: Confidence Intervals (if provided)
        if confidence_intervals is not None:
            lower_bound, upper_bound = confidence_intervals
            # Align confidence intervals with other data
            lower_bound = lower_bound[:len(time_indices)]
            upper_bound = upper_bound[:len(time_indices)]
            
            axes[1, 1].plot(time_indices, actual_values, label='Actual', linewidth=2, color='blue')
            axes[1, 1].plot(time_indices, predicted_values, label='Predicted', linewidth=2, color='red')
            axes[1, 1].fill_between(time_indices, lower_bound, upper_bound, color='gray', alpha=0.3, label='Confidence Interval')
            axes[1, 1].set_title('Forecast with Confidence Intervals')
            axes[1, 1].set_xlabel('Time')
            axes[1, 1].set_ylabel('Values')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        else:
            # If no confidence intervals, show residuals vs predicted
            axes[1, 1].scatter(predicted_values, residuals, alpha=0.7, color='purple')
            axes[1, 1].axhline(y=0, color='red', linestyle='--', linewidth=1)
            axes[1, 1].set_title('Residuals vs Predicted Values')
            axes[1, 1].set_xlabel('Predicted Values')
            axes[1, 1].set_ylabel('Residuals')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        logger.error(f"Error in plotting TEFN forecast: {str(e)}")
        raise

def generate_sample_data() -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    Generate sample time series data for demonstration.
    
    Returns:
        Tuple of (time_indices, actual_values, predicted_values)
    """
    # Generate time indices (e.g., 37 time points)
    time_indices = np.arange(37)
    
    # Generate actual values (e.g., sine wave with noise)
    actual_values = np.sin(0.2 * time_indices) + 0.1 * np.random.randn(37)
    
    # Generate predicted values (e.g., sine wave with different phase and noise)
    predicted_values = np.sin(0.2 * time_indices + 0.5) + 0.1 * np.random.randn(37)
    
    return time_indices, actual_values, predicted_values

def demonstrate_dimension_mismatch_fix():
    """
    Demonstrate the fix for dimension mismatch error.
    This simulates the error scenario where x has shape (6,) and y has shape (37,).
    """
    print("Demonstrating TEFN visualization with dimension mismatch fix...")
    
    # Simulate the problematic data - mismatched dimensions
    # This is what would cause the "x and y must have same first dimension" error
    mismatched_time_indices = np.arange(6)  # Shape (6,)
    actual_values = np.sin(0.2 * np.arange(37)) + 0.1 * np.random.randn(37)  # Shape (37,)
    predicted_values = np.sin(0.2 * np.arange(37) + 0.5) + 0.1 * np.random.randn(37)  # Shape (37,)
    
    print(f"Mismatched shapes - Time: {mismatched_time_indices.shape}, Actual: {actual_values.shape}, Predicted: {predicted_values.shape}")
    
    try:
        # This would normally cause the dimension mismatch error
        # plot_tefn_forecast(mismatched_time_indices, actual_values, predicted_values)
        # Instead, we'll fix the dimensions first
        print("Fixing dimension mismatch...")
        
        # Fix: Align all arrays to the same length
        fixed_time_indices, fixed_actual, fixed_predicted = validate_and_align_dimensions(
            mismatched_time_indices, actual_values, predicted_values
        )
        
        # Now plot with aligned dimensions
        plot_tefn_forecast(
            fixed_time_indices, 
            fixed_actual, 
            fixed_predicted,
            title="TEFN Forecast - Dimension Mismatch Fixed"
        )
        
        print("Visualization completed successfully with dimension mismatch fixed!")
        
    except Exception as e:
        logger.error(f"Error in demonstration: {str(e)}")

if __name__ == "__main__":
    # Demonstrate the fix for dimension mismatch
    demonstrate_dimension_mismatch_fix()
    
    # Also show a normal case with properly aligned data
    print("\nShowing normal case with properly aligned data...")
    time_indices, actual_values, predicted_values = generate_sample_data()
    
    # Add confidence intervals for demonstration
    lower_bound = predicted_values - 0.2
    upper_bound = predicted_values + 0.2
    
    plot_tefn_forecast(
        time_indices, 
        actual_values, 
        predicted_values,
        confidence_intervals=(lower_bound, upper_bound),
        title="TEFN Forecast - Normal Case"
    )
    
    print("All visualizations completed successfully!")