"""
Hyperparameter Tuning Automation for ML Log Prediction
Optuna-based automated hyperparameter optimization with model-specific strategies
"""

import optuna
import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Callable, Tuple
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import warnings
import json
import pickle
from datetime import datetime

# Suppress Optuna logs for cleaner output
optuna.logging.set_verbosity(optuna.logging.WARNING)

class HyperparameterTuner:
    """Automated hyperparameter tuning using Optuna."""
    
    def __init__(self, study_name: Optional[str] = None, storage: Optional[str] = None):
        """
        Initialize hyperparameter tuner.
        
        Args:
            study_name: Name for the Optuna study
            storage: Storage URL for study persistence
        """
        self.study_name = study_name or f"ml_log_prediction_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.storage = storage
        self.study = None
        self.best_params = {}
        self.tuning_history = []
        
    def create_study(self, direction: str = 'minimize', sampler: Optional[optuna.samplers.BaseSampler] = None) -> None:
        """
        Create Optuna study for optimization.
        
        Args:
            direction: Optimization direction ('minimize' or 'maximize')
            sampler: Optuna sampler (default: TPESampler)
        """
        if sampler is None:
            sampler = optuna.samplers.TPESampler(seed=42)
        
        self.study = optuna.create_study(
            study_name=self.study_name,
            direction=direction,
            sampler=sampler,
            storage=self.storage,
            load_if_exists=True
        )
        
        print(f"📊 Created Optuna study: {self.study_name}")
        print(f"   Direction: {direction}")
        print(f"   Sampler: {type(sampler).__name__}")
    
    def suggest_hyperparameters(self, trial: optuna.Trial, model_type: str) -> Dict[str, Any]:
        """
        Suggest hyperparameters based on model type.
        
        Args:
            trial: Optuna trial object
            model_type: Type of model ('transformer', 'mrnn', 'saits', 'brits', 'enhanced_unet')
            
        Returns:
            Dictionary of suggested hyperparameters
        """
        if model_type == 'transformer':
            return self._suggest_transformer_params(trial)
        elif model_type == 'mrnn':
            return self._suggest_mrnn_params(trial)
        elif model_type == 'saits':
            return self._suggest_saits_params(trial)
        elif model_type == 'brits':
            return self._suggest_brits_params(trial)
        elif model_type == 'enhanced_unet':
            return self._suggest_enhanced_unet_params(trial)
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def _suggest_transformer_params(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Suggest hyperparameters for Transformer model."""
        return {
            'd_model': trial.suggest_categorical('d_model', [128, 256, 512]),
            'n_heads': trial.suggest_categorical('n_heads', [4, 8, 16]),
            'n_encoder_layers': trial.suggest_int('n_encoder_layers', 2, 8),
            'd_ff': trial.suggest_categorical('d_ff', [512, 1024, 2048]),
            'dropout': trial.suggest_float('dropout', 0.05, 0.3),
            'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
            'epochs': trial.suggest_int('epochs', 50, 150)
        }
    
    def _suggest_mrnn_params(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Suggest hyperparameters for mRNN model."""
        n_layers = trial.suggest_int('n_layers', 2, 4)
        
        # Suggest hidden sizes for each layer
        hidden_sizes = []
        for i in range(3):  # Fixed to 3 resolution levels
            size = trial.suggest_categorical(f'hidden_size_{i}', [32, 64, 128, 256])
            hidden_sizes.append(size)
        
        return {
            'hidden_sizes': hidden_sizes,
            'n_layers': n_layers,
            'bidirectional': trial.suggest_categorical('bidirectional', [True, False]),
            'attention_dim': trial.suggest_categorical('attention_dim', [64, 128, 256]),
            'dropout': trial.suggest_float('dropout', 0.1, 0.4),
            'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
            'epochs': trial.suggest_int('epochs', 50, 100)
        }
    
    def _suggest_saits_params(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Suggest hyperparameters for SAITS model."""
        return {
            'n_layers': trial.suggest_int('n_layers', 1, 4),
            'd_model': trial.suggest_categorical('d_model', [128, 256, 512]),
            'n_heads': trial.suggest_categorical('n_heads', [2, 4, 8]),
            'dropout': trial.suggest_float('dropout', 0.05, 0.2),
            'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
            'epochs': trial.suggest_int('epochs', 30, 80)
        }
    
    def _suggest_brits_params(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Suggest hyperparameters for BRITS model."""
        return {
            'rnn_hidden_size': trial.suggest_categorical('rnn_hidden_size', [64, 128, 256]),
            'learning_rate': trial.suggest_float('learning_rate', 1e-4, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
            'epochs': trial.suggest_int('epochs', 30, 80)
        }
    
    def _suggest_enhanced_unet_params(self, trial: optuna.Trial) -> Dict[str, Any]:
        """Suggest hyperparameters for Enhanced UNet model."""
        # Suggest channel progression
        base_channels = trial.suggest_categorical('base_channels', [16, 32, 64])
        channels = [base_channels * (2 ** i) for i in range(4)]
        
        return {
            'channels': tuple(channels),
            'strides': trial.suggest_categorical('strides', [(2, 2, 2), (1, 2, 2), (2, 1, 2)]),
            'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
            'epochs': trial.suggest_int('epochs', 30, 80)
        }
    
    def objective_function(self, trial: optuna.Trial, model_class: Any, model_type: str,
                          train_data: torch.Tensor, truth_data: torch.Tensor,
                          validation_split: float = 0.2) -> float:
        """
        Objective function for Optuna optimization.
        
        Args:
            trial: Optuna trial object
            model_class: Model class to optimize
            model_type: Type of model
            train_data: Training data
            truth_data: Ground truth data
            validation_split: Fraction of data to use for validation
            
        Returns:
            Objective value (lower is better for minimization)
        """
        try:
            # Suggest hyperparameters
            params = self.suggest_hyperparameters(trial, model_type)
            
            # Add fixed parameters
            params.update({
                'n_features': train_data.shape[-1],
                'sequence_len': train_data.shape[1]
            })
            
            # Split data for validation
            split_idx = int(len(train_data) * (1 - validation_split))
            train_split = train_data[:split_idx]
            truth_split = truth_data[:split_idx]
            val_split = train_data[split_idx:]
            val_truth = truth_data[split_idx:]
            
            # Initialize and train model
            model = model_class(**params)
            model.fit(train_split, truth_split)
            
            # Predict on validation set
            predictions = model.predict(val_split)
            
            # Calculate validation metrics
            val_mae = self._calculate_mae(predictions, val_truth)
            
            # Store trial information
            trial.set_user_attr('mae', val_mae)
            trial.set_user_attr('params', params)
            
            return val_mae
            
        except Exception as e:
            print(f"❌ Trial failed: {e}")
            # Return a large value for failed trials
            return float('inf')
    
    def _calculate_mae(self, predictions: torch.Tensor, truth: torch.Tensor) -> float:
        """Calculate Mean Absolute Error."""
        # Handle NaN values
        mask = ~torch.isnan(truth)
        if mask.sum() == 0:
            return float('inf')
        
        pred_clean = predictions[mask]
        truth_clean = truth[mask]
        
        mae = torch.mean(torch.abs(pred_clean - truth_clean)).item()
        return mae
    
    def optimize(self, model_class: Any, model_type: str,
                train_data: torch.Tensor, truth_data: torch.Tensor,
                n_trials: int = 50, timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Run hyperparameter optimization.
        
        Args:
            model_class: Model class to optimize
            model_type: Type of model
            train_data: Training data
            truth_data: Ground truth data
            n_trials: Number of optimization trials
            timeout: Timeout in seconds
            
        Returns:
            Best hyperparameters and optimization results
        """
        if self.study is None:
            self.create_study()
        
        print(f"🚀 Starting hyperparameter optimization for {model_type}")
        print(f"   Trials: {n_trials}")
        print(f"   Data shape: {train_data.shape}")
        
        # Define objective function for this optimization
        def objective(trial):
            return self.objective_function(trial, model_class, model_type, train_data, truth_data)
        
        # Run optimization
        self.study.optimize(objective, n_trials=n_trials, timeout=timeout)
        
        # Get best results
        best_trial = self.study.best_trial
        self.best_params = best_trial.params
        
        results = {
            'best_params': self.best_params,
            'best_value': best_trial.value,
            'n_trials': len(self.study.trials),
            'study_name': self.study_name,
            'optimization_history': [trial.value for trial in self.study.trials if trial.value is not None]
        }
        
        print(f"✅ Optimization completed!")
        print(f"   Best MAE: {best_trial.value:.6f}")
        print(f"   Best parameters: {self.best_params}")
        
        return results
    
    def cross_validate_best_params(self, model_class: Any, 
                                  train_data: torch.Tensor, truth_data: torch.Tensor,
                                  n_splits: int = 5) -> Dict[str, Any]:
        """
        Cross-validate the best hyperparameters using time series splits.
        
        Args:
            model_class: Model class
            train_data: Training data
            truth_data: Ground truth data
            n_splits: Number of cross-validation splits
            
        Returns:
            Cross-validation results
        """
        if not self.best_params:
            raise ValueError("No best parameters available. Run optimization first.")
        
        print(f"🔄 Cross-validating best parameters with {n_splits} splits")
        
        # Time series split
        tscv = TimeSeriesSplit(n_splits=n_splits)
        
        cv_scores = []
        fold_results = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(train_data)):
            print(f"   Fold {fold + 1}/{n_splits}")
            
            # Split data
            train_fold = train_data[train_idx]
            truth_fold = truth_data[train_idx]
            val_fold = train_data[val_idx]
            val_truth = truth_data[val_idx]
            
            # Add fixed parameters
            params = self.best_params.copy()
            params.update({
                'n_features': train_data.shape[-1],
                'sequence_len': train_data.shape[1]
            })
            
            try:
                # Train model
                model = model_class(**params)
                model.fit(train_fold, truth_fold)
                
                # Predict
                predictions = model.predict(val_fold)
                
                # Calculate metrics
                mae = self._calculate_mae(predictions, val_truth)
                cv_scores.append(mae)
                
                fold_results.append({
                    'fold': fold + 1,
                    'mae': mae,
                    'train_size': len(train_fold),
                    'val_size': len(val_fold)
                })
                
            except Exception as e:
                print(f"   ❌ Fold {fold + 1} failed: {e}")
                cv_scores.append(float('inf'))
        
        # Calculate statistics
        valid_scores = [score for score in cv_scores if score != float('inf')]
        
        if valid_scores:
            cv_results = {
                'mean_mae': np.mean(valid_scores),
                'std_mae': np.std(valid_scores),
                'min_mae': np.min(valid_scores),
                'max_mae': np.max(valid_scores),
                'fold_results': fold_results,
                'n_successful_folds': len(valid_scores),
                'best_params': self.best_params
            }
            
            print(f"✅ Cross-validation completed!")
            print(f"   Mean MAE: {cv_results['mean_mae']:.6f} ± {cv_results['std_mae']:.6f}")
            print(f"   Successful folds: {len(valid_scores)}/{n_splits}")
            
        else:
            cv_results = {
                'error': 'All folds failed',
                'fold_results': fold_results,
                'best_params': self.best_params
            }
            print("❌ All cross-validation folds failed")
        
        return cv_results
    
    def save_study(self, filepath: str) -> None:
        """Save the Optuna study to file."""
        if self.study is None:
            print("⚠️ No study to save")
            return
        
        study_data = {
            'study_name': self.study_name,
            'best_params': self.best_params,
            'trials': [
                {
                    'number': trial.number,
                    'value': trial.value,
                    'params': trial.params,
                    'state': trial.state.name
                }
                for trial in self.study.trials
            ]
        }
        
        with open(filepath, 'w') as f:
            json.dump(study_data, f, indent=2)
        
        print(f"💾 Study saved to: {filepath}")
    
    def load_study(self, filepath: str) -> None:
        """Load a previously saved study."""
        with open(filepath, 'r') as f:
            study_data = json.load(f)
        
        self.study_name = study_data['study_name']
        self.best_params = study_data['best_params']
        
        print(f"📂 Study loaded from: {filepath}")
        print(f"   Study name: {self.study_name}")
        print(f"   Best parameters: {self.best_params}")

class AutoTuner:
    """Automated tuning for multiple models."""
    
    def __init__(self):
        """Initialize auto tuner."""
        self.tuners = {}
        self.results = {}
    
    def tune_all_models(self, model_configs: Dict[str, Dict], 
                       train_data: torch.Tensor, truth_data: torch.Tensor,
                       n_trials: int = 30) -> Dict[str, Any]:
        """
        Tune hyperparameters for all specified models.
        
        Args:
            model_configs: Dictionary of model configurations
            train_data: Training data
            truth_data: Ground truth data
            n_trials: Number of trials per model
            
        Returns:
            Tuning results for all models
        """
        print(f"🎯 Auto-tuning {len(model_configs)} models")
        
        for model_name, config in model_configs.items():
            print(f"\n🔧 Tuning {model_name}...")
            
            try:
                # Create tuner for this model
                tuner = HyperparameterTuner(study_name=f"{model_name}_tuning")
                
                # Run optimization
                results = tuner.optimize(
                    model_class=config['model_class'],
                    model_type=model_name,
                    train_data=train_data,
                    truth_data=truth_data,
                    n_trials=n_trials
                )
                
                # Store results
                self.tuners[model_name] = tuner
                self.results[model_name] = results
                
            except Exception as e:
                print(f"❌ Failed to tune {model_name}: {e}")
                self.results[model_name] = {'error': str(e)}
        
        # Print summary
        self._print_tuning_summary()
        
        return self.results
    
    def _print_tuning_summary(self) -> None:
        """Print summary of tuning results."""
        print("\n📊 Hyperparameter Tuning Summary:")
        print("=" * 50)
        
        for model_name, results in self.results.items():
            if 'error' in results:
                print(f"❌ {model_name}: Failed ({results['error']})")
            else:
                print(f"✅ {model_name}: Best MAE = {results['best_value']:.6f}")
        
        # Find best overall model
        valid_results = {k: v for k, v in self.results.items() if 'error' not in v}
        if valid_results:
            best_model = min(valid_results.items(), key=lambda x: x[1]['best_value'])
            print(f"\n🏆 Best overall model: {best_model[0]} (MAE: {best_model[1]['best_value']:.6f})")
    
    def get_best_model_config(self) -> Tuple[str, Dict[str, Any]]:
        """Get the best model configuration."""
        valid_results = {k: v for k, v in self.results.items() if 'error' not in v}
        
        if not valid_results:
            raise ValueError("No successful tuning results available")
        
        best_model_name, best_results = min(valid_results.items(), key=lambda x: x[1]['best_value'])
        
        return best_model_name, best_results['best_params']
