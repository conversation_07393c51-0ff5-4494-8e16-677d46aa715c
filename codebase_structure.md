# ML Log Prediction - Codebase Structure

## Overview
This is a machine learning-based log prediction system designed for well log data analysis and prediction. The system provides a comprehensive workflow for loading, processing, and predicting well log data using various machine learning models.

## Main Pipeline (`main.py`)

The main entry point of the application that orchestrates the entire ML log prediction workflow.

### Key Components:
1. **Data Loading**
   - Loads LAS (Log ASCII Standard) files from a user-selected directory
   - Handles multiple wells and log curves

2. **Configuration**
   - Feature and target log selection
   - Training/prediction strategy setup
   - Model hyperparameter configuration

3. **Data Processing**
   - Data cleaning and quality control
   - Well separation for training and validation

4. **Machine Learning**
   - Supports multiple ML models (XGBoost, etc.)
   - Batch execution of multiple models
   - Model performance evaluation and comparison

5. **Output & Visualization**
   - Results saving
   - Performance metrics visualization
   - Quality control reports

## Core Modules

### `data_handler.py`
- Handles LAS file I/O operations
- Data loading and preprocessing
- Results writing to LAS files

### `config_handler.py`
- User interface for configuration
- File and directory selection
- Model and hyperparameter configuration

### `ml_core.py`
- Core machine learning functionality
- Implements prediction pipelines
- Model registry for different ML algorithms

### `reporting.py`
- Generates QC reports
- Creates visualizations and plots
- Produces model comparison analyses

## Directory Structure

```
.
├── main.py                # Main application entry point
├── data_handler.py        # Data loading and preprocessing
├── config_handler.py      # Configuration management
├── ml_core.py             # Core ML functionality
├── reporting.py           # Reporting and visualization
├── tests/                 # Test files (excluded from version control)
├── catboost_info/         # CatBoost model outputs (excluded)
├── archives/              # Archive files (excluded)
└── docs/                  # Documentation (excluded)
```

## Getting Started

1. Install the required dependencies
2. Run `main.py`
3. Follow the interactive prompts to:
   - Select input LAS files
   - Configure model parameters
   - Run predictions
   - View and save results

## Dependencies
- Python 3.x
- Required packages (see requirements.txt)

## Notes
- The `.gitignore` file is configured to exclude non-essential directories
- The system is designed to be extensible for adding new ML models
- Comprehensive error handling and user feedback is implemented throughout
