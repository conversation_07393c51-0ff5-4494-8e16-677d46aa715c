#!/usr/bin/env python3
"""
Simple test to validate TEFN visualization fixes
"""

import numpy as np

def debug_array_shapes(name, array):
    """Debug helper to print array shape information."""
    if array is not None:
        print(f"🔍 {name} shape: {array.shape}, dtype: {array.dtype}")
    else:
        print(f"🔍 {name}: None")

def validate_and_reshape_forecasting_data(historical_data, true_future, predictions, model_name):
    """Validate and reshape forecasting data to ensure compatibility."""
    print(f"🔧 Validating and reshaping data for {model_name} forecasting visualization...")
    
    # Debug original shapes
    debug_array_shapes("Historical Data", historical_data)
    debug_array_shapes("True Future", true_future)
    debug_array_shapes("Predictions", predictions)
    
    # Handle 3D data by selecting first feature if needed
    if len(historical_data.shape) == 3:
        print(f"   📝 Historical data is 3D, selecting first feature (shape: {historical_data.shape})")
        historical_data = historical_data[:, :, 0]  # Select first feature
        
    if len(true_future.shape) == 3:
        print(f"   📝 True future is 3D, selecting first feature (shape: {true_future.shape})")
        true_future = true_future[:, :, 0]  # Select first feature
        
    if len(predictions.shape) == 3:
        print(f"   📝 Predictions are 3D, selecting first feature (shape: {predictions.shape})")
        predictions = predictions[:, :, 0]  # Select first feature
    
    # Debug reshaped shapes
    print("   ✅ After reshaping:")
    debug_array_shapes("   Historical Data", historical_data)
    debug_array_shapes("   True Future", true_future)
    debug_array_shapes("   Predictions", predictions)
    
    return historical_data, true_future, predictions

def test_dimension_fix():
    """Test the core dimension fix logic."""
    print("🧪 Testing TEFN Dimension Fix")
    print("="*50)
    
    # Create test data that mimics the problematic case
    # This simulates the PhysioNet dataset structure
    n_samples = 5
    n_hist_steps = 42  # Historical steps
    n_future_steps = 6  # Prediction steps (this was causing the (6,) dimension)
    n_features = 37    # Features (this was causing the (37,) dimension)
    
    print(f"📊 Creating test data:")
    print(f"   Samples: {n_samples}")
    print(f"   Historical steps: {n_hist_steps}")
    print(f"   Future steps: {n_future_steps}")
    print(f"   Features: {n_features}")
    
    # Create 3D data (this is the root cause of the dimension mismatch)
    historical_data = np.random.randn(n_samples, n_hist_steps, n_features)
    true_future = np.random.randn(n_samples, n_future_steps, n_features)
    predictions = np.random.randn(n_samples, n_future_steps, n_features)
    
    print(f"\n🔍 Original problematic shapes:")
    debug_array_shapes("Historical Data", historical_data)
    debug_array_shapes("True Future", true_future)
    debug_array_shapes("Predictions", predictions)
    
    # This would cause the error: "x and y must have same first dimension, but have shapes (6,) and (37,)"
    # Because when we select sample[0] from 3D data, we get (time_steps, features)
    # So true_future[0] would be (6, 37) and if we try to plot it directly, 
    # matplotlib sees x=(6,) and y=(37,) which causes the error
    
    print(f"\n⚠️  Demonstrating the original problem:")
    sample_idx = 0
    problematic_true_sample = true_future[sample_idx]  # Shape: (6, 37)
    print(f"   true_future[{sample_idx}] shape: {problematic_true_sample.shape}")
    print(f"   This would cause: x=(6,) and y=(37,) dimension mismatch in plotting")
    
    # Now test our fix
    print(f"\n🔧 Applying our fix:")
    try:
        hist_fixed, true_fixed, pred_fixed = validate_and_reshape_forecasting_data(
            historical_data, true_future, predictions, "TEST_TEFN"
        )
        
        print(f"\n✅ Fix successful! New shapes:")
        debug_array_shapes("Fixed Historical", hist_fixed)
        debug_array_shapes("Fixed True Future", true_fixed)
        debug_array_shapes("Fixed Predictions", pred_fixed)
        
        # Verify the fix
        sample_fixed = true_fixed[sample_idx]  # Should now be (6,) - 1D
        print(f"\n🎯 Verification:")
        print(f"   true_future_fixed[{sample_idx}] shape: {sample_fixed.shape}")
        print(f"   This is now 1D and will plot correctly!")
        
        # Test plotting compatibility
        hist_len = hist_fixed.shape[1]
        future_len = true_fixed.shape[1]
        future_indices = list(range(hist_len, hist_len + future_len))
        
        print(f"\n📈 Plotting compatibility test:")
        print(f"   future_indices length: {len(future_indices)}")
        print(f"   true_sample length: {len(sample_fixed)}")
        print(f"   Dimensions match: {len(future_indices) == len(sample_fixed)}")
        
        if len(future_indices) == len(sample_fixed):
            print("   ✅ Plotting will work correctly!")
            return True
        else:
            print("   ❌ Still have dimension mismatch")
            return False
            
    except Exception as e:
        print(f"❌ Fix failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test various edge cases."""
    print(f"\n🧪 Testing Edge Cases")
    print("="*50)
    
    # Test case 1: Already 2D data (should pass through unchanged)
    print(f"\n📝 Test Case 1: 2D data")
    hist_2d = np.random.randn(5, 42)
    true_2d = np.random.randn(5, 6)
    pred_2d = np.random.randn(5, 6)
    
    try:
        h, t, p = validate_and_reshape_forecasting_data(hist_2d, true_2d, pred_2d, "2D_TEST")
        print("   ✅ 2D data handled correctly")
    except Exception as e:
        print(f"   ❌ 2D data failed: {e}")
    
    # Test case 2: Mismatched dimensions
    print(f"\n📝 Test Case 2: Mismatched dimensions")
    hist_mismatch = np.random.randn(5, 42, 37)
    true_mismatch = np.random.randn(3, 6, 37)  # Different sample count
    pred_mismatch = np.random.randn(5, 8, 37)  # Different time steps
    
    try:
        h, t, p = validate_and_reshape_forecasting_data(hist_mismatch, true_mismatch, pred_mismatch, "MISMATCH_TEST")
        print("   ✅ Mismatched dimensions handled")
    except Exception as e:
        print(f"   ❌ Mismatched dimensions failed: {e}")

def main():
    """Run the tests."""
    print("🚀 TEFN Visualization Fix Validation")
    print("="*80)
    
    # Test the core fix
    success = test_dimension_fix()
    
    # Test edge cases
    test_edge_cases()
    
    # Summary
    print(f"\n📊 Summary")
    print("="*50)
    if success:
        print("✅ Core dimension fix is working correctly!")
        print("🎉 The TEFN visualization error should be resolved.")
        print("\n📋 What was fixed:")
        print("   • 3D data (samples, time_steps, features) is now properly handled")
        print("   • First feature is selected for visualization when data is 3D")
        print("   • Dimension validation prevents plotting errors")
        print("   • Enhanced error handling provides better diagnostics")
        print("   • Multiple visualization options for better insights")
    else:
        print("❌ Core dimension fix needs more work.")
    
    print(f"\n🔧 Next steps:")
    print("   1. Run the actual TEFN forecasting example")
    print("   2. Check that plots are generated without errors")
    print("   3. Verify the visualization quality and insights")

if __name__ == "__main__":
    main()
