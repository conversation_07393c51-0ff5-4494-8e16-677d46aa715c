#!/usr/bin/env python3
"""
Quick test script to verify transformer optimization
"""

def test_optimized_transformer():
    """Test the optimized transformer configuration."""
    
    print("🧪 Testing optimized transformer configuration...")
    
    try:
        from models.advanced_models.transformer_model import TransformerModel
        
        # Test with optimized defaults
        print("   Creating model with optimized defaults...")
        model = TransformerModel(
            n_features=5,
            sequence_len=64,
            epochs=1  # Just test initialization
        )
        
        print("✅ Model created successfully!")
        print(f"   Parameters: {model._estimate_parameters():,}")
        print(f"   Memory estimate: {model._estimate_memory_mb():.1f} MB")
        print(f"   Configuration:")
        print(f"     - d_model: {model.d_model} (was 256)")
        print(f"     - n_heads: {model.n_heads} (was 8)")
        print(f"     - n_encoder_layers: {model.n_encoder_layers} (was 6)")
        print(f"     - d_ff: {model.d_ff} (was 1024)")
        print(f"     - batch_size: {model.batch_size} (was 32)")
        
        # Calculate theoretical speedup
        original_ops = 6 * (64**2 * 256 * 8 + 64 * 256 * 1024 * 2)  # Original
        optimized_ops = 4 * (64**2 * 192 * 6 + 64 * 192 * 768 * 2)  # Optimized
        speedup = (original_ops - optimized_ops) / original_ops * 100
        
        print(f"\n📊 Theoretical Performance Improvement:")
        print(f"   Original operations: {original_ops:,}")
        print(f"   Optimized operations: {optimized_ops:,}")
        print(f"   Expected speedup: {speedup:.1f}%")
        
        # Test parameter estimation
        param_count = model._estimate_parameters()
        print(f"\n🔢 Parameter Analysis:")
        print(f"   Total parameters: {param_count:,}")
        print(f"   Estimated memory: {model._estimate_memory_mb():.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_optimized_transformer()
    if success:
        print("\n🎉 Optimization test completed successfully!")
    else:
        print("\n💥 Optimization test failed!")
