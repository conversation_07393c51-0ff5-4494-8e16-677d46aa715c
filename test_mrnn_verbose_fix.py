#!/usr/bin/env python3
"""
Test script to verify the verbose AttributeError fix in the optimized mRNN model.
This script tests that the model can be initialized and trained without errors.
"""

import torch
import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mrnn_verbose_fix():
    """Test that MRNNModel can be trained without AttributeError: 'verbose'."""
    print("Testing mRNN verbose AttributeError fix...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Initialize model with verbose parameter
        model = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=2,  # Small number for testing
            batch_size=16,
            verbose=True  # Explicitly test verbose parameter
        )
        print("Model initialization successful")
        
        # Verify verbose attribute exists
        assert hasattr(model, 'verbose'), "verbose attribute missing"
        assert model.verbose == True, "verbose attribute not set correctly"
        print("Verbose attribute present and configured correctly")
        
        # Create small test dataset
        n_samples = 100
        sequence_len = 64
        n_features = 5
        
        # Generate synthetic data with missing values
        np.random.seed(42)
        torch.manual_seed(42)
        
        # Create training data with missing values
        train_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        
        # Introduce missing values (NaN) - about 20% missing
        missing_mask = torch.rand(n_samples, sequence_len, n_features) < 0.2
        train_data[missing_mask] = float('nan')
        
        # Create ground truth (complete data)
        truth_data = torch.randn(n_samples, sequence_len, n_features, dtype=torch.float32)
        
        print(f"Test dataset shape: {train_data.shape}")
        print(f"Missing values: {torch.isnan(train_data).sum().item()}")
        
        # Test training - this should NOT raise AttributeError about verbose
        print("Starting training test...")
        model.fit(
            train_data=train_data,
            truth_data=truth_data,
            epochs=2,  # Very small for testing
            batch_size=16,
            patience=1,
            validation_split=0.2
        )
        
        print("Training completed successfully!")
        
        # Test prediction
        print("Testing prediction...")
        test_input = train_data[:10]  # Use first 10 samples
        predictions = model.predict(test_input)
        print(f"Prediction successful! Output shape: {predictions.shape}")
        
        return True
        
    except AttributeError as e:
        if 'verbose' in str(e):
            print(f"FAILED: verbose AttributeError still present: {e}")
            return False
        else:
            print(f"Different AttributeError: {e}")
            raise
    except Exception as e:
        print(f"Training failed with error: {e}")
        import traceback
        traceback.print_exc()
        raise

def test_verbose_functionality():
    """Test that verbose parameter controls output correctly."""
    print("\nTesting verbose functionality...")
    
    try:
        from models.advanced_models.mrnn_model import MRNNModel
        
        # Test with verbose=True
        model_verbose = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=1,
            batch_size=16,
            verbose=True
        )
        
        # Test with verbose=False
        model_quiet = MRNNModel(
            n_features=5,
            sequence_len=64,
            epochs=1,
            batch_size=16,
            verbose=False
        )
        
        print("Both verbose configurations work")
        
        assert model_verbose.verbose == True
        assert model_quiet.verbose == False
        print("Verbose settings correctly configured")
        
        return True
        
    except Exception as e:
        print(f"Verbose functionality test failed: {e}")
        raise

if __name__ == "__main__":
    print("Testing mRNN verbose AttributeError Fix")
    print("=" * 50)
    
    try:
        # Run tests
        success1 = test_verbose_functionality()
        success2 = test_mrnn_verbose_fix()
        
        if success1 and success2:
            print("\n" + "=" * 50)
            print("ALL TESTS PASSED!")
            print("verbose AttributeError has been successfully fixed")
            print("Training completes without errors")
        else:
            print("\n" + "=" * 50)
            print("TESTS FAILED!")
            sys.exit(1)
            
    except Exception as e:
        print(f"\nTest suite failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)